'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { ArrowLeft, Database, Loader2 } from 'lucide-react';

export default function SchemaPage() {
  const [loading, setLoading] = useState(true);
  const [schema, setSchema] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSchema = async () => {
      try {
        const response = await fetch('/api/schema');
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Failed to fetch schema');
        }

        setSchema(data);
      } catch (err: any) {
        console.error('Schema fetch error:', err);
        setError(err.message || 'An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchSchema();
  }, []);

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-6">
        <Link href="/" className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Home
        </Link>
      </div>

      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Database Schema</h1>
        
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : error ? (
          <div className="bg-destructive/10 border border-destructive/20 text-destructive rounded-lg p-4 mb-6">
            <h3 className="font-semibold mb-2">Error</h3>
            <p>{error}</p>
          </div>
        ) : (
          <div>
            <div className="bg-muted/50 rounded-lg p-6 mb-8">
              <h2 className="text-xl font-semibold mb-4">Table: {schema.table_name}</h2>
              
              <h3 className="font-semibold mb-2">Columns:</h3>
              <ul className="list-disc pl-6 mb-6">
                {schema.columns.map((column: string) => (
                  <li key={column} className="mb-1">{column}</li>
                ))}
              </ul>
              
              {schema.table_definition && (
                <div className="mb-6">
                  <h3 className="font-semibold mb-2">Table Definition:</h3>
                  <pre className="bg-muted p-3 rounded text-xs overflow-auto max-h-60">
                    {schema.table_definition}
                  </pre>
                </div>
              )}
              
              {schema.sample_document && (
                <div>
                  <h3 className="font-semibold mb-2">Sample Document:</h3>
                  <pre className="bg-muted p-3 rounded text-xs overflow-auto max-h-96">
                    {JSON.stringify(schema.sample_document, null, 2)}
                  </pre>
                </div>
              )}
            </div>
            
            <div className="flex justify-between">
              <Link 
                href="/seed"
                className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-3 text-sm font-medium text-primary-foreground hover:bg-primary/90 transition-colors focus-ring"
              >
                <Database className="mr-2 h-4 w-4" />
                Go to Seed Tool
              </Link>
              
              <Link 
                href="/test"
                className="inline-flex items-center justify-center rounded-md border border-border px-6 py-3 text-sm font-medium hover:bg-muted transition-colors focus-ring"
              >
                View All Documents
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
