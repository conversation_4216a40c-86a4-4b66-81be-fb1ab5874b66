import { MetadataRoute } from 'next'
import { mainCategories } from '@/lib/categoryMappings'
import { getAllDocuments, SopDocument } from '@/lib/supabaseUtils'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = 'https://duvainsight.com'

  // Fetch all documents for dynamic post URLs
  const documents = await getAllDocuments();

  // Create post entries for sitemap
  const posts = documents.map((doc: SopDocument) => {
    const mainCategorySlug = mainCategories[doc.mainCategory as keyof typeof mainCategories];
    return {
      url: `${baseUrl}/post/${mainCategorySlug}/${doc.slug}`,
      lastModified: new Date(doc.createdAt),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    };
  });

  // Create category entries for sitemap
  const categories = Object.values(mainCategories).map(categorySlug => ({
    url: `${baseUrl}/category/${categorySlug}`,
    lastModified: new Date(),
    changeFrequency: 'daily' as const,
    priority: 0.7,
  }));

  const pages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/newsletter`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/archive`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/trending`,
      lastModified: new Date(),
      changeFrequency: 'hourly' as const,
      priority: 0.9,
    },
  ]

  return [...pages, ...categories, ...posts]
}
