import { notFound } from 'next/navigation'
import { Metadata } from 'next'
import { ArticleCard } from '@/components/article-card'
import FilterButtons from '@/components/filter-buttons'
import { getAllDocuments, SopDocument } from '@/lib/supabaseUtils'
import { mainCategories, getMainCategoryFromSubCategory } from '@/lib/categoryMappings'
import Link from 'next/link'
import { Breadcrumb } from '@/components/breadcrumb'
import { BackButton } from '@/components/back-button'

type SubCategoryPageProps = {
  params: Promise<{
    slug: string
  }>
}

// Sub-category styling and descriptions
const subCategoryConfig: Record<string, { name: string; description: string }> = {
  // Technology
  'web-mobile-development': {
    name: 'Web & Mobile Development',
    description: 'Building digital experiences for browsers and mobile devices',
  },
  'web-design': {
    name: 'Web Design',
    description: 'Visual design, user experience, and interface design for websites',
  },
  'ai-machine-learning': {
    name: 'AI & Machine Learning',
    description: 'Artificial intelligence and machine learning technologies',
  },
  'cybersecurity-privacy': {
    name: 'Cybersecurity & Privacy',
    description: 'Protecting digital assets and ensuring data privacy',
  },
  'devops-cloud': {
    name: 'DevOps & Cloud',
    description: 'Streamlining development operations and cloud infrastructure',
  },
  'hardware-iot': {
    name: 'Hardware & IoT',
    description: 'Physical computing devices and Internet of Things',
  },

  // Business
  'strategy-planning': {
    name: 'Strategy & Planning',
    description: 'Business strategy development and execution planning',
  },
  'leadership-management': {
    name: 'Leadership & Management',
    description: 'Effective leadership practices and management techniques',
  },
  'marketing-sales': {
    name: 'Marketing & Sales',
    description: 'Customer acquisition and revenue generation strategies',
  },
  'entrepreneurship': {
    name: 'Entrepreneurship',
    description: 'Starting and growing new business ventures',
  },
  'operations': {
    name: 'Operations',
    description: 'Optimizing business processes and operations',
  },

  // Health
  'medical-research': {
    name: 'Medical Research',
    description: 'Advances in medical science and healthcare research',
  },
  'wellness-fitness': {
    name: 'Wellness & Fitness',
    description: 'Physical health, exercise, and wellness practices',
  },
  'mental-health': {
    name: 'Mental Health',
    description: 'Psychological wellbeing and mental health topics',
  },
  'healthcare-systems': {
    name: 'Healthcare Systems',
    description: 'Healthcare delivery, policy, and infrastructure',
  },
  'nutrition': {
    name: 'Nutrition',
    description: 'Diet, food science, and nutritional health',
  },

  // Culture
  'arts-entertainment': {
    name: 'Arts & Entertainment',
    description: 'Creative expression and entertainment media',
  },
  'social-trends': {
    name: 'Social Trends',
    description: 'Emerging social patterns and cultural movements',
  },
  'education': {
    name: 'Education',
    description: 'Learning methodologies and educational systems',
  },
  'media': {
    name: 'Media',
    description: 'News, publishing, and media analysis',
  },
  'lifestyle': {
    name: 'Lifestyle',
    description: 'Daily living, home, and personal interests',
  },

  // Finance
  'investing': {
    name: 'Investing',
    description: 'Investment strategies and financial markets',
  },
  'cryptocurrency': {
    name: 'Cryptocurrency',
    description: 'Digital currencies and blockchain technology',
  },
  'personal-finance': {
    name: 'Personal Finance',
    description: 'Individual financial planning and management',
  },
  'banking': {
    name: 'Banking',
    description: 'Banking systems, services, and innovations',
  },
  'economics': {
    name: 'Economics',
    description: 'Economic theory, policy, and global trends',
  },

  // Creative
  'design': {
    name: 'Design',
    description: 'Visual, UX, and product design principles',
  },
  'writing': {
    name: 'Writing',
    description: 'Content creation and written communication',
  },
  'visual-arts': {
    name: 'Visual Arts',
    description: 'Fine art, illustration, and visual expression',
  },
  'music-audio': {
    name: 'Music & Audio',
    description: 'Sound production, music, and audio content',
  },
  'film-video': {
    name: 'Film & Video',
    description: 'Video production, filmmaking, and visual storytelling',
  },
};

export async function generateMetadata({ params }: SubCategoryPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const slug = resolvedParams.slug;

  const subCategory = subCategoryConfig[slug];

  if (!subCategory) {
    return {
      title: 'Sub-Category Not Found',
    }
  }

  const mainCategorySlug = getMainCategoryFromSubCategory(slug);
  const mainCategoryName = Object.keys(mainCategories).find(
    key => mainCategories[key as keyof typeof mainCategories] === mainCategorySlug
  );

  return {
    title: `${subCategory.name} | ${mainCategoryName} | DuvaInsights`,
    description: subCategory.description,
  }
}

export default async function SubCategoryPage({ params }: SubCategoryPageProps) {
  const resolvedParams = await params;
  const slug = resolvedParams.slug;

  const subCategory = subCategoryConfig[slug];

  if (!subCategory) {
    notFound()
  }

  // Get main category info
  const mainCategorySlug = getMainCategoryFromSubCategory(slug);
  const mainCategoryName = Object.keys(mainCategories).find(
    key => mainCategories[key as keyof typeof mainCategories] === mainCategorySlug
  );

  if (!mainCategorySlug || !mainCategoryName) {
    notFound()
  }

  // Fetch all documents
  const allDocuments = await getAllDocuments();

  // Filter documents to only include those with the current subcategory
  const posts = allDocuments
    .filter((doc: SopDocument) => doc.subCategory === slug)
    .map((doc: SopDocument) => ({
      id: doc.id,
      title: doc.title,
      excerpt: doc.metadata?.metadata?.primary_objective,
      category: mainCategorySlug || 'technology', // Use the current main category for links
      subCategory: slug, // Use the current subcategory for links
      slug: doc.slug,
      author: doc.metadata?.metadata?.author,
      date: new Date(doc.createdAt).toISOString().split('T')[0],
      readingTime: '5 min read',
    }));

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex flex-col sm:flex-row justify-between items-start gap-2">
          <BackButton />
          <Breadcrumb />
        </div>
      </div>

      {/* Header */}
      <header className="bg-gradient-to-b from-muted/50 to-background">
        <div className="container mx-auto px-4 py-16">
          <div className="mx-auto max-w-3xl text-center">
            <div className="mb-2">
              <Link href={`/category/${mainCategorySlug}`} className="text-sm text-muted-foreground hover:underline">
                {mainCategoryName}
              </Link>
              <span className="mx-2 text-muted-foreground">›</span>
              <span className="text-sm font-medium">{subCategory.name}</span>
            </div>
            <h1 className="mb-4 text-4xl font-bold md:text-5xl">{subCategory.name}</h1>
            <p className="text-lg text-muted-foreground">{subCategory.description}</p>
          </div>
        </div>
      </header>

      {/* Filters and Sort */}
      <div className="border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              {posts.length} articles
            </p>
            <FilterButtons />
          </div>
        </div>
      </div>

      {/* Articles Grid */}
      <section className="container mx-auto px-4 py-12">
        {posts.length > 0 ? (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {posts.map((post: any) => (
              <div key={post.id} className="h-full">
                <ArticleCard article={post} />
              </div>
            ))}
          </div>
        ) : (
          <div className="py-20 text-center">
            <p className="text-muted-foreground">No articles in this sub-category yet. Check back soon!</p>
          </div>
        )}
      </section>
    </div>
  )
}