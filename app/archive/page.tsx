'use server'

import { Suspense } from 'react'
import { Metadata } from 'next'
import { ArticleCard } from '@/components/article-card'
import { Calendar, Filter, Search, ChevronLeft, ChevronRight } from 'lucide-react'
import { getAllDocuments, SopDocument } from '@/lib/supabaseUtils'
import ArchiveClient from './archive-client'
import { Breadcrumb } from '@/components/breadcrumb'
import { BackButton } from '@/components/back-button'

const categories = [
  { value: 'all', label: 'All Categories' },
  { value: 'technology', label: 'Technology' },
  { value: 'business', label: 'Business' },
  { value: 'health', label: 'Health' },
  { value: 'culture', label: 'Culture' },
  { value: 'finance', label: 'Finance' },
  { value: 'creative', label: 'Creative' },
]

const sortOptions = [
  { value: 'newest', label: 'Newest First' },
  { value: 'oldest', label: 'Oldest First' },
  { value: 'popular', label: 'Most Popular' },
]

export default async function ArchivePage() {
  // Fetch all documents from the database
  const documents = await getAllDocuments();

  // Format documents for display
  const formattedPosts = documents.map((doc: SopDocument) => {
    return {
      id: doc.id,
      title: doc.title || 'Untitled',
      excerpt: doc.metadata?.metadata?.primary_objective || 'No description available',
      category: doc.mainCategorySlug || 'technology',
      subCategory: doc.subCategory,
      slug: doc.slug, // Use the document slug
      author: doc.metadata?.metadata?.author || 'Unknown Author',
      date: new Date(doc.createdAt).toISOString().split('T')[0],
      readingTime: '5 min read',
      imageUrl: doc.image_url,
    };
  });

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex flex-col sm:flex-row justify-between items-start gap-2">
          <BackButton />
          <Breadcrumb />
        </div>
      </div>

      {/* Header */}
      <section className="bg-gradient-to-b from-muted/50 to-background py-16">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-3xl text-center">
            <div className="mb-6 inline-flex items-center rounded-full bg-primary/10 px-4 py-2">
              <Calendar className="mr-2 h-4 w-4 text-primary" />
              <span className="text-sm font-medium text-primary">Article Archive</span>
            </div>
            <h1 className="mb-4 text-4xl font-bold md:text-5xl">
              Explore All Insights
            </h1>
            <p className="text-lg text-muted-foreground">
              Browse through our complete collection of trend analysis and insights
            </p>
          </div>
        </div>
      </section>

      {/* Client Component for filtering and pagination */}
      <Suspense fallback={<div className="container mx-auto px-4 py-12 text-center">Loading...</div>}>
        <ArchiveClient
          initialPosts={formattedPosts}
          categories={categories}
          sortOptions={sortOptions}
        />
      </Suspense>
    </div>
  )
}
