import Link from 'next/link'
import { <PERSON><PERSON><PERSON>, Clock, TrendingUp, Sparkles } from 'lucide-react'
import { CategoryGrid } from '@/components/category-grid'
import { NewsletterSignup } from '@/components/newsletter-signup'
import { TrendingTopics } from '@/components/trending-topics'
import { ArticleCard } from '@/components/article-card'
import { getAllDocuments, SopDocument } from '@/lib/supabaseUtils'

export default async function HomePage() {
  // Fetch real documents from Supabase
  const documents = await getAllDocuments();
  console.log(`HomePage: Fetched ${documents.length} documents`);

  // Format documents for display
  const formattedPosts = documents.map((doc: SopDocument) => {
    // Use the mainCategorySlug directly from the document
    const mainCategorySlug = doc.mainCategorySlug || 'uncategorized';

    return {
      id: doc.id,
      title: doc.title || 'Untitled',
      excerpt: doc.metadata?.metadata?.primary_objective || 'No description available',
      category: mainCategorySlug,
      subCategory: doc.subCategory,
      slug: doc.id, // Using the document ID as the slug
      author: doc.metadata?.metadata?.author || 'Duva Team',
      date: new Date(doc.createdAt).toISOString().split('T')[0],
      readingTime: '5 min read',
      image: '/images/placeholder.jpg', // Default placeholder image
    };
  });

  // Use the first document as featured post if available, otherwise use a placeholder
  const featuredPost = formattedPosts.length > 0 ? formattedPosts[0] : {
    id: '1',
    title: 'No Featured Content Available',
    excerpt: 'Please add documents to your database to see content here.',
    category: 'technology',
    slug: 'no-content',
    author: 'System',
    date: new Date().toISOString().split('T')[0],
    readingTime: '0 min read',
    image: '/images/placeholder.jpg',
  };

  // Get the latest posts (excluding the featured one)
  const latestPosts = formattedPosts.length > 1
    ? formattedPosts.slice(1, 4) // Get up to 3 posts after the featured one
    : [];
  return (
    <div className="animate-fade-in">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-b from-muted/50 to-background">
        <div className="container mx-auto px-4 py-16 md:py-24">
          <div className="mx-auto max-w-3xl text-center">
            <div className="mb-6 inline-flex items-center rounded-full bg-primary/10 px-4 py-2">
              <Sparkles className="mr-2 h-4 w-4 text-primary" />
              <span className="text-sm font-medium text-primary">Discover Tomorrow's Trends Today</span>
            </div>
            <h1 className="mb-6 text-4xl font-bold tracking-tight md:text-6xl">
              Your Window Into the <span className="text-gradient">Future</span>
            </h1>
            <p className="mb-8 text-lg text-muted-foreground md:text-xl">
              Curated insights and analysis on emerging trends in technology, business, health, and culture.
              Stay ahead of the curve with DuvaInsights.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/trending"
                className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-3 text-sm font-medium text-primary-foreground hover:bg-primary/90 transition-colors focus-ring"
              >
                Explore Trending Topics
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
              <Link
                href="/about"
                className="inline-flex items-center justify-center rounded-md border border-border px-6 py-3 text-sm font-medium hover:bg-muted transition-colors focus-ring"
              >
                Learn More
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Article */}
      <section className="container mx-auto px-4 py-12">
        <div className="mb-8 flex items-center justify-between">
          <h2 className="text-2xl font-bold">Featured Insight</h2>
        </div>
        <article className="group relative overflow-hidden rounded-lg border bg-card hover:shadow-lg transition-all duration-300">
          <Link href={`/post/${featuredPost.category}/${featuredPost.slug}`}>
            <div className="grid gap-6 md:grid-cols-2">
              <div className="aspect-[16/9] bg-gradient-to-br from-primary/20 to-primary/5 md:aspect-auto" />
              <div className="p-6 md:p-8 flex flex-col">
                <div className="mb-4 flex items-center gap-4 text-sm text-muted-foreground">
                  <span className="rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary capitalize leading-normal">
                    {featuredPost.category}
                  </span>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span className="leading-normal align-middle">{featuredPost.readingTime}</span>
                  </div>
                </div>
                <h3 className="mb-3 text-2xl font-bold group-hover:text-primary transition-colors md:text-3xl leading-tight">
                  {featuredPost.title}
                </h3>
                <p className="mb-4 text-muted-foreground line-clamp-3 leading-normal">
                  {featuredPost.excerpt}
                </p>
                <div className="flex items-center justify-between mt-auto">
                  <div className="text-sm text-muted-foreground leading-normal"> 
                    <span className="font-medium align-baseline">{featuredPost.author}</span>
                    <span className="mx-2 align-baseline">·</span>
                    <time className="align-baseline">{new Date(featuredPost.date).toLocaleDateString()}</time>
                  </div>
                  <ArrowRight className="h-5 w-5 text-primary opacity-0 group-hover:opacity-100 transition-opacity" />
                </div>
              </div>
            </div>
          </Link>
        </article>
      </section>

      {/* Latest Articles */}
      <section className="container mx-auto px-4 py-12">
        <div className="mb-8 flex items-center justify-between">
          <h2 className="text-2xl font-bold">Latest Insights</h2>
          <Link
            href="/archive"
            className="text-sm font-medium text-muted-foreground hover:text-primary transition-colors"
          >
            View all articles
            <ArrowRight className="ml-1 inline-block h-4 w-4" />
          </Link>
        </div>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {latestPosts.map((post: any) => (
            <div key={post.id} className="h-full">
              <ArticleCard article={post} />
            </div>
          ))}
        </div>
      </section>

      {/* Category Grid */}
      <section className="bg-gradient-to-b from-muted/50 to-background py-16">
        <div className="container mx-auto px-4">
          <div className="mb-8 text-center">
            <h2 className="mb-4 text-3xl font-bold">Explore by Category</h2>
            <p className="text-muted-foreground">
              Deep dive into specific domains and discover targeted insights
            </p>
          </div>
          <CategoryGrid />
        </div>
      </section>

      {/* Trending Topics */}
      <section className="container mx-auto px-4 py-12">
        <div className="mx-auto max-w-lg">
          <TrendingTopics />
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="container mx-auto px-4 py-12">
        <NewsletterSignup />
      </section>
    </div>
  )
}
