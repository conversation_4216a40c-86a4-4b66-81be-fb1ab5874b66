import { notFound } from 'next/navigation'
import { Metadata } from 'next'
import { ArticleCard } from '@/components/article-card'
import FilterButtons from '@/components/filter-buttons'
import { getAllDocuments, SopDocument } from '@/lib/supabaseUtils'
import { mainCategories, getSubcategoriesForMainCategory } from '@/lib/categoryMappings'
import { Breadcrumb } from '@/components/breadcrumb'
import { BackButton } from '@/components/back-button'

// Category styling and descriptions
const categoryConfig = {
  technology: {
    name: 'Technology',
    description: 'AI, software, hardware, and digital innovation shaping our future',
    color: 'text-blue-600 dark:text-blue-400',
    bgColor: 'bg-blue-100 dark:bg-blue-900/20',
  },
  business: {
    name: 'Business',
    description: 'Leadership, strategy, and corporate trends transforming industries',
    color: 'text-purple-600 dark:text-purple-400',
    bgColor: 'bg-purple-100 dark:bg-purple-900/20',
  },
  health: {
    name: 'Health',
    description: 'Wellness, medicine, and biotechnology advancing human potential',
    color: 'text-red-600 dark:text-red-400',
    bgColor: 'bg-red-100 dark:bg-red-900/20',
  },
  culture: {
    name: 'Culture',
    description: 'Society, lifestyle, and cultural shifts defining our era',
    color: 'text-green-600 dark:text-green-400',
    bgColor: 'bg-green-100 dark:bg-green-900/20',
  },
  finance: {
    name: 'Finance',
    description: 'Markets, crypto, and economic trends reshaping wealth',
    color: 'text-yellow-600 dark:text-yellow-400',
    bgColor: 'bg-yellow-100 dark:bg-yellow-900/20',
  },
  creative: {
    name: 'Creative',
    description: 'Design, art, and creative industries pushing boundaries',
    color: 'text-pink-600 dark:text-pink-400',
    bgColor: 'bg-pink-100 dark:bg-pink-900/20',
  },
}

type PageProps = {
  params: Promise<{ slug: string }>;
}

export async function generateMetadata(
  { params }: PageProps
): Promise<Metadata> {
  const resolvedParams = await params;
  const slug = resolvedParams.slug;

  const category = categoryConfig[slug as keyof typeof categoryConfig]

  if (!category) {
    return {
      title: 'Category Not Found',
    }
  }

  return {
    title: `${category.name} Trends & Insights | DuvaInsights`,
    description: category.description,
  }
}

export default async function CategoryPage({ params }: PageProps) {
  const resolvedParams = await params;
  const slug = resolvedParams.slug;

  const category = categoryConfig[slug as keyof typeof categoryConfig]

  if (!category) {
    notFound()
  }

  // Fetch all documents
  const allDocuments = await getAllDocuments();

  // Find the main category name from the slug
  const mainCategoryName = Object.keys(mainCategories).find(
    key => mainCategories[key as keyof typeof mainCategories] === slug
  );

  if (!mainCategoryName) {
    notFound();
  }

  // Get all subcategories for this main category
  const subcategoriesForThisCategory = getSubcategoriesForMainCategory(mainCategoryName);
  console.log(`Subcategories for ${mainCategoryName}:`, subcategoriesForThisCategory);

  // Filter documents by subcategory
  const posts = allDocuments
    .filter((doc: SopDocument) => {
      // Check if the document's subcategory is in the list of subcategories for this main category
      return subcategoriesForThisCategory.includes(doc.subCategory);
    })
    .map((doc: SopDocument) => ({
      id: doc.id,
      title: doc.title,
      excerpt: doc.metadata?.metadata?.primary_objective,
      category: slug, // Use the current category slug for links
      subCategory: doc.subCategory,
      slug: doc.slug,
      author: doc.metadata?.metadata?.author || 'Unknown Author',
      date: new Date(doc.createdAt).toISOString().split('T')[0],
      readingTime: '5 min read', // Could calculate this based on content length
    }));

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex flex-col sm:flex-row justify-between items-start gap-2">
          <BackButton />
          <Breadcrumb />
        </div>
      </div>

      {/* Header */}
      <header className="bg-gradient-to-b from-muted/50 to-background">
        <div className="container mx-auto px-4 py-16">
          <div className="mx-auto max-w-3xl text-center">
            <div className={`mb-6 inline-flex rounded-lg p-3 ${category.bgColor}`}>
              <div className={`h-8 w-8 ${category.color}`} />
            </div>
            <h1 className="mb-4 text-4xl font-bold md:text-5xl">{category.name}</h1>
            <p className="text-lg text-muted-foreground">{category.description}</p>
          </div>
        </div>
      </header>

      {/* Filters and Sort */}
      <div className="border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              {posts.length} articles
            </p>
            <FilterButtons />
          </div>
        </div>
      </div>

      {/* Articles Grid */}
      <section className="container mx-auto px-4 py-12">
        {posts.length > 0 ? (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {posts.map((post: any) => (
              <div key={post.id} className="h-full">
                <ArticleCard article={post} />
              </div>
            ))}
          </div>
        ) : (
          <div className="py-20 text-center">
            <p className="text-muted-foreground">No articles in this category yet. Check back soon!</p>
          </div>
        )}
      </section>
    </div>
  )
}
