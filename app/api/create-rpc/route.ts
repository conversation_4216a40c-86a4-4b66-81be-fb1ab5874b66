import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET() {
  try {
    // Create an RPC function to get table definition
    const { data, error } = await supabase.rpc('create_get_table_definition_function');

    if (error) {
      console.error('Error creating RPC function:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'RPC function created successfully',
      result: data
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ 
      error: error.message || 'An unexpected error occurred',
      stack: error.stack
    }, { 
      status: 500 
    });
  }
}
