import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET() {
  try {
    // Get the schema of the sop_documents table
    const { data: columns, error: columnsError } = await supabase
      .from('sop_documents')
      .select('*')
      .limit(1);

    if (columnsError) {
      console.error('Error fetching table schema:', columnsError);
      return NextResponse.json({ error: columnsError.message }, { status: 500 });
    }

    // Get the first document to examine its structure
    const { data: firstDoc, error: docError } = await supabase
      .from('sop_documents')
      .select('*')
      .limit(1)
      .single();

    if (docError && docError.code !== 'PGRST116') { // PGRST116 is "No rows returned" which is fine
      console.error('Error fetching first document:', docError);
    }

    // Get table definition from Postgres if the RPC function exists
    let tableInfo = null;
    try {
      const { data, error } = await supabase
        .rpc('get_table_definition', { table_name: 'sop_documents' })
        .single();

      if (error) {
        console.error('Error fetching table definition:', error);
      } else {
        tableInfo = data;
      }
    } catch (e) {
      console.error('RPC function might not exist:', e);
      // Continue anyway, this is just additional info
    }

    // Check if the table exists by trying to get its structure
    const { error: tableExistsError } = await supabase
      .from('sop_documents')
      .select('count(*)', { count: 'exact', head: true });

    const tableExists = !tableExistsError;

    return NextResponse.json({
      table_name: 'sop_documents',
      table_exists: tableExists,
      table_error: tableExistsError ? tableExistsError.message : null,
      columns: columns ? Object.keys(columns[0] || {}) : [],
      sample_document: firstDoc || null,
      table_definition: tableInfo || null
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json({
      error: error.message || 'An unexpected error occurred',
      stack: error.stack
    }, {
      status: 500
    });
  }
}
