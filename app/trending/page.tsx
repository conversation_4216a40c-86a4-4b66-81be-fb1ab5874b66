import { Metadata } from 'next'
import { TrendingUp, <PERSON>, Z<PERSON>, Clock, ArrowUpRight, Activity } from 'lucide-react'
import Link from 'next/link'
import { ArticleCard } from '@/components/article-card'
import { getAllDocuments, SopDocument } from '@/lib/supabaseUtils'
import { getCategoryColorClasses } from '@/lib/shared/categoryColors'
import { Breadcrumb } from '@/components/breadcrumb'
import { BackButton } from '@/components/back-button'

export const metadata: Metadata = {
  title: 'Trending Topics & Insights | DuvaInsights',
  description: 'Discover what\'s trending now across technology, business, health, and culture.',
}

// Define types for better type safety
interface CategoryCount {
  [key: string]: number;
}

interface TrendingTopic {
  id: string;
  title: string;
  category: string;
  posts: number;
  growth: string;
  description: string;
  hot: boolean;
}

interface TrendingPost {
  id: string;
  title: string;
  excerpt: string;
  category: string;
  author: string;
  date: string;
  readingTime: string;
  views: string;
  trend: string;
}

interface RisingCategory {
  name: string;
  growth: string;
  posts: number;
}

// Real trending topics based on document categories
const generateTrendingTopics = async (): Promise<TrendingTopic[]> => {
  const documents = await getAllDocuments();

  // Group documents by subcategory
  const categoryCounts = documents.reduce((acc: CategoryCount, doc: SopDocument) => {
    const category = doc.subCategory || 'uncategorized';
    acc[category] = (acc[category] || 0) + 1;
    return acc;
  }, {});

  // Convert to array and sort by count
  const sortedCategories = Object.entries(categoryCounts)
    .map(([category, count]) => ({
      category,
      count: count as number
    }))
    .sort((a, b) => b.count - a.count);

  // Map to trending topics format
  return sortedCategories.slice(0, 6).map((item, index) => {
    // Find a document in this category to get more details
    const sampleDoc = documents.find((doc: SopDocument) => doc.subCategory === item.category);

    // Generate random growth percentage between 25% and 95%
    const growth = `+${Math.floor(25 + Math.random() * 70)}%`;

    // Determine if it's a hot topic (first 3 are hot)
    const hot = index < 3;

    return {
      id: `topic-${index + 1}`,
      title: item.category
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' '),
      category: sampleDoc?.mainCategorySlug || 'technology',
      posts: item.count,
      growth: growth,
      description: sampleDoc?.metadata?.metadata?.primary_objective ||
        `Trending insights and analysis on ${item.category.replace(/-/g, ' ')}`,
      hot: hot,
    };
  });
}

// Generate trending posts from real documents
const generateTrendingPosts = async (): Promise<TrendingPost[]> => {
  const documents = await getAllDocuments();

  // Sort by a combination of factors to simulate "trending" posts
  // In a real app, this would be based on view counts, engagement, etc.
  const sortedDocs = [...documents].sort(() => 0.5 - Math.random());

  return sortedDocs.slice(0, 3).map((doc: SopDocument, index) => {
    // Generate random view counts between 10K and 50K
    const viewCount = Math.floor(10 + Math.random() * 40);

    // Generate random trend percentage between 50% and 130%
    const trendPercentage = Math.floor(50 + Math.random() * 80);

    // Generate random reading time between 5 and 12 minutes
    const readingTime = Math.floor(5 + Math.random() * 7);

    // Calculate a date within the last week
    const date = new Date();
    date.setDate(date.getDate() - Math.floor(Math.random() * 7));
    const formattedDate = date.toISOString().split('T')[0];

    return {
      id: doc.id,
      title: doc.title || 'Untitled Document',
      excerpt: doc.metadata?.metadata?.primary_objective || 'No description available',
      category: doc.mainCategorySlug || 'technology',
      author: doc.metadata?.metadata?.author || 'DuvaInsights Team',
      date: formattedDate,
      readingTime: `${readingTime} min read`,
      views: `${viewCount}.${Math.floor(Math.random() * 9)}K`,
      trend: `+${trendPercentage}%`,
    };
  });
}

// Generate rising categories based on main categories of documents
const generateRisingCategories = async (): Promise<RisingCategory[]> => {
  const documents = await getAllDocuments();

  // Group documents by main category
  const categoryCounts = documents.reduce((acc: CategoryCount, doc: SopDocument) => {
    const category = doc.mainCategory || 'Uncategorized';
    acc[category] = (acc[category] || 0) + 1;
    return acc;
  }, {});

  // Convert to array and sort by count
  return Object.entries(categoryCounts)
    .map(([name, count]) => ({
      name,
      growth: `+${Math.floor(25 + Math.random() * 25)}%`,
      posts: count as number
    }))
    .sort((a, b) => b.posts - a.posts)
    .slice(0, 4);
}

export default async function TrendingPage() {
  // Fetch real data
  const trendingTopics = await generateTrendingTopics();
  const trendingPosts = await generateTrendingPosts();
  const risingCategories = await generateRisingCategories();

  // Calculate metrics based on real data
  const activeTopics = trendingTopics.reduce((sum, topic) => sum + topic.posts, 0);
  const newThisWeek = Math.floor(activeTopics * 0.15); // Assume 15% are new this week

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex flex-col sm:flex-row justify-between items-start gap-2">
          <BackButton fallbackUrl="/" fallbackLabel="Back to Home" />
          <Breadcrumb />
        </div>
      </div>

      {/* Hero Section */}
      <section className="bg-gradient-to-b from-muted/50 to-background py-16">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-3xl text-center">
            <div className="mb-6 inline-flex items-center rounded-full bg-red-100 dark:bg-red-900/20 px-4 py-2">
              <Flame className="mr-2 h-4 w-4 text-red-600 dark:text-red-400" />
              <span className="text-sm font-medium text-red-600 dark:text-red-400">What's Trending Now</span>
            </div>
            <h1 className="mb-4 text-4xl font-bold md:text-5xl">
              Trending Topics & <span className="text-gradient">Hot Insights</span>
            </h1>
            <p className="text-lg text-muted-foreground">
              Real-time tracking of the most popular and fastest-growing trends across all categories
            </p>
          </div>
        </div>
      </section>

      {/* Trending Metrics */}
      <section className="container mx-auto px-4 py-8">
        <div className="grid gap-4 md:grid-cols-4">
          <div className="rounded-lg border bg-card p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Active Topics</p>
                <p className="text-2xl font-bold">{activeTopics}</p>
              </div>
              <Activity className="h-8 w-8 text-primary opacity-20" />
            </div>
          </div>
          <div className="rounded-lg border bg-card p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Weekly Growth</p>
                {(() => {
                  // Use the color of the most popular category
                  const topCategory = trendingTopics[0]?.category || 'technology';
                  const colorClasses = getCategoryColorClasses(topCategory);
                  return (
                    <p className={`text-2xl font-bold ${colorClasses.text}`}>+34%</p>
                  );
                })()}
              </div>
              {(() => {
                const topCategory = trendingTopics[0]?.category || 'technology';
                const colorClasses = getCategoryColorClasses(topCategory);
                return (
                  <TrendingUp className={`h-8 w-8 ${colorClasses.text} opacity-20`} />
                );
              })()}
            </div>
          </div>
          <div className="rounded-lg border bg-card p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Hot Topics</p>
                <p className="text-2xl font-bold">{trendingTopics.filter(t => t.hot).length}</p>
              </div>
              <Flame className="h-8 w-8 text-orange-500 opacity-20" />
            </div>
          </div>
          <div className="rounded-lg border bg-card p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">New This Week</p>
                <p className="text-2xl font-bold">{newThisWeek}</p>
              </div>
              <Zap className="h-8 w-8 text-yellow-500 opacity-20" />
            </div>
          </div>
        </div>
      </section>

      {/* Trending Topics Grid */}
      <section className="container mx-auto px-4 py-12">
        <h2 className="mb-8 text-2xl font-bold">Top Trending Topics</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {trendingTopics.map((topic, index) => (
            <Link
              key={topic.id}
              href={`/subcategory/${topic.title.toLowerCase().replace(/\s+/g, '-')}`}
              className="group relative overflow-hidden rounded-lg border bg-card p-6 hover:shadow-md transition-all"
            >
              <div className="mb-4 flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <span className="text-3xl font-bold text-muted-foreground">
                    {String(index + 1).padStart(2, '0')}
                  </span>
                  {topic.hot && (
                    <Flame className="h-5 w-5 text-orange-500" />
                  )}
                </div>
                {(() => {
                  const colorClasses = getCategoryColorClasses(topic.category);
                  return (
                    <div className={`flex items-center gap-1 text-sm font-medium ${colorClasses.text}`}>
                      <Zap className="h-3 w-3" />
                      {topic.growth}
                    </div>
                  );
                })()}
              </div>

              <h3 className="mb-2 text-lg font-semibold group-hover:text-primary transition-colors">
                {topic.title}
              </h3>

              <p className="mb-3 text-sm text-muted-foreground line-clamp-2">
                {topic.description}
              </p>

              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">
                  {topic.posts} articles
                </span>
                {(() => {
                  const colorClasses = getCategoryColorClasses(topic.category);
                  return (
                    <span className={`rounded-full ${colorClasses.bg} px-2 py-0.5 text-xs font-medium ${colorClasses.text} capitalize`}>
                      {topic.category}
                    </span>
                  );
                })()}
              </div>

              <ArrowUpRight className="absolute top-4 right-4 h-4 w-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity" />
            </Link>
          ))}
        </div>
      </section>

      {/* Trending Articles */}
      <section className="bg-gradient-to-b from-muted/50 to-background py-16">
        <div className="container mx-auto px-4">
          <div className="mb-8 flex items-center justify-between">
            <h2 className="text-2xl font-bold">Most Read This Week</h2>
            <Link
              href="/archive"
              className="text-sm font-medium text-muted-foreground hover:text-primary transition-colors"
            >
              View all popular articles →
            </Link>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {trendingPosts.map((post) => (
              <article key={post.id} className="group relative overflow-hidden rounded-lg border bg-card hover:shadow-md transition-all duration-300">
                <Link href={`/post/${post.category}/${post.id}`}>
                  <div className="aspect-[16/9] bg-gradient-to-br from-primary/10 to-transparent" />
                  <div className="p-6">
                    <div className="mb-3 flex items-center gap-3 text-sm text-muted-foreground">
                      {(() => {
                        const colorClasses = getCategoryColorClasses(post.category);
                        return (
                          <span className={`rounded-full ${colorClasses.bg} px-2.5 py-0.5 text-xs font-medium ${colorClasses.text} capitalize`}>
                            {post.category}
                          </span>
                        );
                      })()}
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>{post.readingTime}</span>
                      </div>
                    </div>
                    <h3 className="mb-2 text-xl font-bold line-clamp-2 group-hover:text-primary transition-colors">
                      {post.title}
                    </h3>
                    <p className="mb-4 text-sm text-muted-foreground line-clamp-2">
                      {post.excerpt}
                    </p>
                    <div className="flex items-center justify-between text-sm">
                      <div className="text-muted-foreground">
                        <span className="font-medium">{post.views} views</span>
                        <span className="mx-2">·</span>
                        {(() => {
                          const colorClasses = getCategoryColorClasses(post.category);
                          return (
                            <span className={colorClasses.text}>{post.trend}</span>
                          );
                        })()}
                      </div>
                      <ArrowUpRight className="h-4 w-4 text-primary opacity-0 group-hover:opacity-100 transition-opacity" />
                    </div>
                  </div>
                </Link>
              </article>
            ))}
          </div>
        </div>
      </section>

      {/* Rising Categories */}
      <section className="container mx-auto px-4 py-16">
        <h2 className="mb-8 text-2xl font-bold">Rising Categories</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {risingCategories.map((category) => (
            <Link
              key={category.name}
              href={`/category/${category.name.toLowerCase()}`}
              className="group rounded-lg border bg-card p-6 hover:shadow-md transition-all"
            >
              <div className="mb-2 flex items-center justify-between">
                <h3 className="text-lg font-semibold">{category.name}</h3>
                {(() => {
                  const colorClasses = getCategoryColorClasses(category.name.toLowerCase());
                  return (
                    <span className={`text-sm font-medium ${colorClasses.text}`}>
                      {category.growth}
                    </span>
                  );
                })()}
              </div>
              <p className="text-sm text-muted-foreground">
                {category.posts} trending articles
              </p>
              <ArrowUpRight className="mt-2 h-4 w-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity" />
            </Link>
          ))}
        </div>
      </section>

      {/* CTA */}
      <section className="bg-gradient-to-t from-muted/50 to-background py-16">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="mb-4 text-3xl font-bold">Never Miss a Trend</h2>
            <p className="mb-8 text-muted-foreground">
              Get weekly updates on the fastest-growing topics and emerging trends
            </p>
            <Link
              href="/newsletter"
              className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-3 text-sm font-medium text-primary-foreground hover:bg-primary/90 transition-colors"
            >
              Subscribe to Trending Insights
              <ArrowUpRight className="ml-2 h-4 w-4" />
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
