import { notFound } from 'next/navigation'
import { Metadata } from 'next'
import DocumentViewer from '@/app/post/viewer-components/document-viewer'
import { getDocumentBySlug } from '@/lib/supabaseUtils'
import { SopDocument as SharedSopDocument } from '@/lib/shared/types'
// Temporarily commented out for fixing 404 issue
// import { validateCategoryHierarchy } from '@/lib/categoryMappings'

type PageProps = {
  params: Promise<{
    category: string  // Main category slug
    slug: string      // Document ID
  }>
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const { category, slug } = resolvedParams;

  // Get document from Supabase using slug
  const document = await getDocumentBySlug(slug, category)

  if (!document) {
    return { title: 'Document Not Found' }
  }

  return {
    title: `${document.metadata?.metadata?.title || 'Document'} | DuvaInsights`,
    description: document.metadata?.metadata?.primary_objective || undefined
  }
}

export default async function PostDetailPage({ params }: PageProps) {
  const resolvedParams = await params;
  const { category, slug } = resolvedParams;

  // Get document from Supabase using slug
  const document = await getDocumentBySlug(slug, category)

  if (!document) {
    return notFound()
  }

  // For now, we'll skip the category validation to fix the 404 issue
  // This allows documents to be viewed regardless of category in URL
  // We can add proper validation back once the categorization is fixed

  // Commented out validation:
  // const subCategory = document.metadata?.metadata?.category
  // if (!subCategory || !validateCategoryHierarchy(category, subCategory)) {
  //   return notFound()
  // }

  // Convert the document to match the expected SharedSopDocument type
  const convertedDocument: SharedSopDocument = {
    id: document.id,
    created_at: document.created_at || new Date().toISOString(),
    updated_at: document.updated_at || new Date().toISOString(),
    user_id: document.user_id || 'unknown',
    type: document.type as 'technical' | 'non-technical',
    metadata: document.metadata,
    pipeline_outputs: document.pipeline_outputs,
    subCategory: document.subCategory,
    mainCategory: document.mainCategory,
    mainCategorySlug: document.mainCategorySlug
  }

  return <DocumentViewer SOPDocument={convertedDocument} />
}
