import { notFound } from 'next/navigation'
import { Metadata } from 'next'
import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import { MDXRemote } from 'next-mdx-remote/rsc'
import { Clock, Calendar, User, Share2, Bookmark, ChevronLeft } from 'lucide-react'
import Link from 'next/link'
import { TableOfContents } from '@/components/table-of-contents'
import { ShareButtons } from '@/components/share-buttons'
import { RelatedPosts } from '@/components/related-posts'
import { AuthorBio } from '@/components/author-bio'

interface PostPageProps {
  params: {
    category: string
    slug: string
  }
}

async function getPost(category: string, slug: string) {
  try {
    const filePath = path.join(process.cwd(), 'content', category, `${slug}.mdx`)
    const fileContent = fs.readFileSync(filePath, 'utf8')
    const { data, content } = matter(fileContent)
    
    return {
      frontmatter: data,
      content,
    }
  } catch (error) {
    return null
  }
}

export async function generateMetadata({ params }: PostPageProps): Promise<Metadata> {
  const post = await getPost(params.category, params.slug)
  
  if (!post) {
    return {
      title: 'Post Not Found',
    }
  }

  return {
    title: `${post.frontmatter.title} | DuvaInsight`,
    description: post.frontmatter.excerpt,
    openGraph: {
      title: post.frontmatter.title,
      description: post.frontmatter.excerpt,
      type: 'article',
      publishedTime: post.frontmatter.date,
      authors: [post.frontmatter.author],
    },
  }
}

export default async function PostPage({ params }: PostPageProps) {
  const post = await getPost(params.category, params.slug)

  if (!post) {
    notFound()
  }

  const { frontmatter, content } = post

  return (
    <article className="min-h-screen">
      {/* Header */}
      <header className="bg-gradient-to-b from-muted/50 to-background">
        <div className="container mx-auto px-4 py-8">
          <Link
            href={`/category/${params.category}`}
            className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-primary transition-colors mb-6"
          >
            <ChevronLeft className="h-4 w-4" />
            Back to {params.category}
          </Link>
          
          <div className="max-w-4xl mx-auto">
            <div className="mb-6 flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
              <span className="rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary capitalize">
                {frontmatter.category}
              </span>
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <time dateTime={frontmatter.date}>
                  {new Date(frontmatter.date).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                  })}
                </time>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>{frontmatter.readingTime} read</span>
              </div>
              <div className="flex items-center gap-1">
                <User className="h-4 w-4" />
                <span>{frontmatter.author}</span>
              </div>
            </div>
            
            <h1 className="mb-6 text-4xl font-bold md:text-5xl lg:text-6xl">
              {frontmatter.title}
            </h1>
            
            <p className="text-xl text-muted-foreground">
              {frontmatter.excerpt}
            </p>
          </div>
        </div>
      </header>

      {/* Content */}
      <div className="container mx-auto px-4 py-12">
        <div className="grid gap-12 lg:grid-cols-[1fr_300px] max-w-6xl mx-auto">
          <div className="min-w-0">
            <div className="prose prose-lg dark:prose-invert max-w-none">
              <MDXRemote source={content} />
            </div>
            
            {/* Author Bio */}
            <div className="mt-12 border-t pt-12">
              <AuthorBio author={frontmatter.author} />
            </div>
            
            {/* Share Section */}
            <div className="mt-8 border-t pt-8">
              <h3 className="mb-4 text-lg font-semibold">Share this article</h3>
              <ShareButtons
                url={`https://duvainsight.com/post/${params.category}/${params.slug}`}
                title={frontmatter.title}
              />
            </div>
          </div>
          
          {/* Sidebar */}
          <aside className="space-y-8">
            <div className="sticky top-24">
              {/* Actions */}
              <div className="mb-8 flex gap-2">
                <button className="flex-1 inline-flex items-center justify-center gap-2 rounded-md border px-4 py-2 text-sm font-medium hover:bg-muted transition-colors">
                  <Share2 className="h-4 w-4" />
                  Share
                </button>
                <button className="flex-1 inline-flex items-center justify-center gap-2 rounded-md border px-4 py-2 text-sm font-medium hover:bg-muted transition-colors">
                  <Bookmark className="h-4 w-4" />
                  Save
                </button>
              </div>
              
              {/* Table of Contents */}
              <TableOfContents content={content} />
              
              {/* Tags */}
              {frontmatter.tags && (
                <div className="mt-8">
                  <h3 className="mb-3 text-sm font-semibold uppercase text-muted-foreground">
                    Topics
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {frontmatter.tags.map((tag: string) => (
                      <Link
                        key={tag}
                        href={`/tag/${tag.toLowerCase().replace(/\s+/g, '-')}`}
                        className="rounded-full bg-muted px-3 py-1 text-sm hover:bg-muted/80 transition-colors"
                      >
                        {tag}
                      </Link>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </aside>
        </div>
      </div>
      
      {/* Related Posts */}
      <section className="border-t bg-muted/50">
        <div className="container mx-auto px-4 py-16">
          <RelatedPosts
            currentSlug={params.slug}
            category={params.category}
            tags={frontmatter.tags}
          />
        </div>
      </section>
    </article>
  )
}
