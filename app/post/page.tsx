import Link from 'next/link'
import { getAllDocuments, SopDocument } from '@/lib/supabaseUtils'
import { mainCategories } from '@/lib/categoryMappings'

export const revalidate = 60

export default async function PostIndexPage() {
  const documents = await getAllDocuments();

  return (
    <main className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-6">All Documents</h1>
      <ul className="list-disc pl-6 space-y-2">
        {documents.map((doc: SopDocument) => {
          const title = doc.title;
          const classification = doc.classification;
          const mainCategory = doc.mainCategory;

          // Get the main category slug for the URL
          const mainCategorySlug = mainCategories[mainCategory as keyof typeof mainCategories];

          return (
            <li key={doc.id}>
              <Link
                href={`/post/${mainCategorySlug}/${doc.slug}`}
                className="text-blue-600 hover:underline"
              >
                {title}{' '}
                <span className="text-sm text-gray-500">
                  [{classification}]
                </span>
              </Link>
            </li>
          )
        })}
      </ul>
    </main>
  )
}
