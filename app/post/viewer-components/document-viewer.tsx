'use client';

import React, { useState, /* useRef, */ useMemo, useEffect } from 'react'; // Removed useRef
// import { useRouter } from 'next/navigation'; // Removed as not currently used
// import { useReactToPrint } from 'react-to-print'; // Print functionality removed
import {
  // Download, // Removed as print functionality is removed
  BookOpen,
  ShieldAlert,
  CheckSquare,
  BookText,
  Paperclip,
  ListChecks,
  Info,
  AlertCircle,
  Zap,     // Added for Key Takeaways
  Map,     // Added for Roadmap
  Smile,   // Added for Impact
  Lightbulb, // Added for Applications
  Share2,  // Added for Share button
} from 'lucide-react';
import { Breadcrumb } from '@/components/breadcrumb';
import { BackButton } from '@/components/back-button';
import {
  Tabs, // Assuming shadcn Tabs are used
  TabsContent,
  TabsList,
  TabsTrigger
} from "@/components/ui/tabs"; // Reverted to alias
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"; // Reverted to alias
import { Button } from "@/components/ui/button"; // Reverted to alias
import { Separator } from "@/components/ui/separator"; // Reverted to alias
import { TooltipProvider } from "@/components/ui/tooltip"; // Reverted to alias
import {
  DocumentHeader,
  ExecutiveSummary,
  RiskMitigationStrategies,
  Conclusion,
  // InsightModal, // Removed InsightModal
  RoadmapViewer,
  BestPracticesViewer,
  ResourceViewer,
  ActionItemsViewer,
  PracticalApplicationsViewer,
  ContextSummaryViewer,
  KeyTakeawaysViewer,
  EmotionalSocialImpactViewer
} from './';
// Import newly defined types
import { SopDocument, NonTechnicalPipelineOutputsData, TechPipelineOutputsData, ExecutiveSummaryData } from '@/lib/shared/types'; // Reverted to alias
// AI client removed

interface SOPDocumentViewerProps {
  SOPDocument: SopDocument;
}

// Renamed from SOPDocumentViewerContainer to match filename
export const DocumentViewer: React.FC<SOPDocumentViewerProps> = ({ SOPDocument }) => {
  // --- State Management ---
  const [activeTab, setActiveTab] = useState<string>('introduction'); // Default tab
  // const contentRef = useRef<HTMLDivElement | null>(null); // Removed as print functionality is removed

  // --- Navigation ---
  // const router = useRouter(); // Removed as not currently used

  // --- Hooks (Commented out dependencies) ---
  // const { user } = useAuth();
  // const userTier = user?.profile?.tier || 'free';
  // const unifiedSlug = getUnifiedSlug(userTier);
  // const isAdvancedTier = unifiedSlug === 'accelerator' || unifiedSlug === 'orchestrator';

  // --- Event Handlers ---
  // Print functionality (useReactToPrint, handlePrint, triggerPrint) removed
  // AI Insight Generation function removed

  // --- Tab Configuration Logic (Adapted) ---
  const tabsConfig = useMemo(() => {
    // Early return an empty array if document isn't valid
    if (!SOPDocument || !SOPDocument.pipeline_outputs || typeof SOPDocument.pipeline_outputs !== 'object') {
      console.log('DocumentViewer: Invalid SOPDocument or pipeline_outputs', {
        hasSOPDocument: !!SOPDocument,
        hasPipelineOutputs: SOPDocument ? !!SOPDocument.pipeline_outputs : false,
        pipelineOutputsType: SOPDocument ? typeof SOPDocument.pipeline_outputs : 'N/A'
      });
      return [];
    }

    console.log('DocumentViewer: Processing pipeline_outputs', {
      type: SOPDocument.type,
      pipelineOutputsKeys: Object.keys(SOPDocument.pipeline_outputs),
      pipelineOutputsSample: JSON.stringify(SOPDocument.pipeline_outputs).substring(0, 100) + '...'
    });

    // Log the full document structure for debugging
    console.log('Full document structure:', JSON.stringify(SOPDocument.pipeline_outputs));

    const tabs: { value: string; label: string; icon: React.ReactNode; component: React.ReactNode | null }[] = [];
    const pipelineData = SOPDocument.pipeline_outputs;
    const isNonTech = SOPDocument.type === 'non-technical';
    const isTech = SOPDocument.type === 'technical';

    let intro: string | undefined;
    let summary: ExecutiveSummaryData | undefined;

    if (isNonTech) {
        const data = pipelineData as NonTechnicalPipelineOutputsData;
        console.log('Non-technical intro summary check:', {
            has_biz_intro_summary: !!data.biz_intro_summary,
            has_nested_intro_summary: !!data.biz_intro_summary?.biz_intro_summary,
            introduction: data.biz_intro_summary?.biz_intro_summary?.introduction || 'Missing',
            has_executive_summary: !!data.biz_intro_summary?.biz_intro_summary?.executive_summary,
            executive_summary_keys: data.biz_intro_summary?.biz_intro_summary?.executive_summary ?
                Object.keys(data.biz_intro_summary.biz_intro_summary.executive_summary) : []
        });
        intro = data.biz_intro_summary?.biz_intro_summary?.introduction;
        summary = data.biz_intro_summary?.biz_intro_summary?.executive_summary;
    } else {
        const data = pipelineData as TechPipelineOutputsData;

        // Log the raw tech_intro_summary structure to understand its format
        console.log('Raw tech_intro_summary:', JSON.stringify(data.tech_intro_summary));

        // Log the full pipeline_outputs structure to see all available data
        console.log('Full technical pipeline_outputs structure:', {
            keys: Object.keys(data),
            tech_intro_summary_exists: !!data.tech_intro_summary,
            tech_intro_summary_type: typeof data.tech_intro_summary,
            nested_structure: data.tech_intro_summary ? Object.keys(data.tech_intro_summary) : []
        });

        // For technical documents, extract the introduction and executive summary
        // Try different possible structures
        if (data.tech_intro_summary?.tech_intro_summary?.introduction) {
            // Standard nested structure
            intro = data.tech_intro_summary.tech_intro_summary.introduction;
            summary = data.tech_intro_summary.tech_intro_summary.executive_summary;
            console.log('Found standard nested structure for tech_intro_summary');
        } else if ((data.tech_intro_summary as any)?.introduction) {
            // Direct structure (no nesting)
            intro = (data.tech_intro_summary as any).introduction;
            summary = (data.tech_intro_summary as any).executive_summary;
            console.log('Found direct structure for tech_intro_summary');
        } else if ((data.tech_intro_summary as any)?.intro_summary?.introduction) {
            // Alternative nested structure
            intro = (data.tech_intro_summary as any).intro_summary.introduction;
            summary = (data.tech_intro_summary as any).intro_summary.executive_summary;
            console.log('Found alternative nested structure for tech_intro_summary');
        } else {
            // No valid structure found
            console.log('No valid structure found for tech_intro_summary');
            intro = undefined;
            summary = undefined;
        }

        console.log('Technical intro summary check:', {
            has_tech_intro_summary: !!data.tech_intro_summary,
            has_nested_intro_summary: !!data.tech_intro_summary?.tech_intro_summary,
            introduction: intro || 'Missing',
            has_executive_summary: !!summary,
            executive_summary_keys: summary ? Object.keys(summary) : []
        });
    }

    // Introduction Tab with Executive Summary
    console.log('Introduction tab check:', {
        hasIntro: !!intro,
        introType: typeof intro,
        introValue: intro,
        hasSummary: !!summary,
        summaryKeys: summary ? Object.keys(summary) : []
    });

    // Only add the Introduction tab if there's intro content
    if (intro) {
        console.log('Adding Introduction tab with summary');

        // Create a combined component with both introduction and executive summary
        const combinedIntroComponent = (
            <div className="space-y-6">
                <div className="prose prose-gray dark:prose-invert max-w-none">
                    <h3 className="text-lg font-semibold mb-2">Introduction</h3>
                    <p className="leading-relaxed">{intro}</p>
                </div>

                {/* Executive Summary section */}
                <div className="mt-8">
                    <h3 className="text-lg font-semibold mb-4">Executive Summary</h3>
                    <ExecutiveSummary summary={summary || {}} />
                </div>
            </div>
        );

        tabs.push({
            value: 'introduction',
            label: 'Introduction',
            icon: <BookOpen className="h-4 w-4 mr-2 text-blue-500" />,
            component: combinedIntroComponent
        });
    } else {
        console.log('Not adding Introduction tab - no intro content');
    }

    // Non-Technical Specific Tabs
    if (isNonTech) {
        console.log('DocumentViewer: Processing non-technical pipeline_outputs');
        const data = pipelineData as NonTechnicalPipelineOutputsData;
        if (data.biz_key_takeaways?.key_takeaways?.length) {
            tabs.push({ value: 'key-takeaways', label: 'Key Takeaways', icon: <Zap className="h-4 w-4 mr-2 text-purple-500" />, component: <KeyTakeawaysViewer takeaways={data.biz_key_takeaways.key_takeaways} /> });
        }
         if (data.biz_context_summary?.context_summary) {
             tabs.push({ value: 'context', label: 'Context', icon: <Info className="h-4 w-4 mr-2 text-blue-500" />, component: <ContextSummaryViewer context_summary={data.biz_context_summary.context_summary} /> });
         }
        if (data.biz_risk_assessment?.risk_assessment?.risks?.length) {
            tabs.push({ value: 'risks', label: 'Risks', icon: <ShieldAlert className="h-4 w-4 mr-2 text-red-500" />, component: <RiskMitigationStrategies risks={data.biz_risk_assessment.risk_assessment.risks} /> });
        }
        // Note the double nesting for non-tech action items
        const actionItems = data.biz_action_items?.action_items?.action_items;
        if (actionItems?.length) {
            tabs.push({ value: 'action-items', label: 'Action Items', icon: <ListChecks className="h-4 w-4 mr-2 text-orange-500" />, component: <ActionItemsViewer items={actionItems} type={SOPDocument.type} /> });
        }
        if (data.biz_practical_applications?.practical_applications?.length) {
            tabs.push({ value: 'practical-applications', label: 'Applications', icon: <Lightbulb className="h-4 w-4 mr-2 text-amber-400" />, component: <PracticalApplicationsViewer type="non-technical" pipeline_outputs={pipelineData} /> });
        }
        if (data.biz_emotional_or_social_impact?.emotional_or_social_impact &&
            (data.biz_emotional_or_social_impact.emotional_or_social_impact.emotional_effects?.length || data.biz_emotional_or_social_impact.emotional_or_social_impact.social_consequences?.length)) {
           tabs.push({ value: 'impact', label: 'Impact', icon: <Smile className="h-4 w-4 mr-2 text-pink-500" />, component: <EmotionalSocialImpactViewer impact={data.biz_emotional_or_social_impact.emotional_or_social_impact} /> });
        }
        if (data.biz_conclusions?.conclusions && Object.keys(data.biz_conclusions.conclusions).length > 0) {
            if (!tabs.some(tab => tab.value === 'conclusion')) {
                tabs.push({ value: 'conclusion', label: 'Conclusion', icon: <BookText className="h-4 w-4 mr-2 text-indigo-500" />, component: <Conclusion conclusions={data.biz_conclusions.conclusions} /> });
            }
        }
        const roadmapPhases = data.biz_transformation_roadmap?.transformation_roadmap?.phases;
        if (roadmapPhases?.length) {
            tabs.push({ value: 'roadmap', label: 'Roadmap', icon: <Map className="h-4 w-4 mr-2 text-cyan-500" />, component: <RoadmapViewer phases={roadmapPhases} type={SOPDocument.type} /> });
        }
        const bestPractices = data.biz_best_practices?.best_practices_framework?.practices;
        if (bestPractices?.length) {
            tabs.push({ value: 'best-practices', label: 'Best Practices', icon: <CheckSquare className="h-4 w-4 mr-2 text-emerald-500" />, component: <BestPracticesViewer practices={bestPractices} type={SOPDocument.type} /> });
        }
        const resources = data.biz_resources?.resources;
        if (resources?.length) {
            tabs.push({ value: 'resources', label: 'Resources', icon: <Paperclip className="h-4 w-4 mr-2 text-gray-400" />, component: <ResourceViewer resources={resources} type={SOPDocument.type} /> });
        }

    }

    // Technical Specific Tabs
    if (isTech) {
        console.log('DocumentViewer: Processing technical pipeline_outputs');
        const data = pipelineData as TechPipelineOutputsData;

        // Debug technical document structure in detail
        console.log('Technical document structure:', {
            tech_intro_summary: data.tech_intro_summary ?
                `Present with intro: ${!!data.tech_intro_summary.tech_intro_summary?.introduction}` : 'Missing',
            tech_practical_applications: data.tech_practical_applications ?
                JSON.stringify(data.tech_practical_applications) : 'Missing',
            tech_practical_applications_type: data.tech_practical_applications ?
                typeof data.tech_practical_applications : 'N/A',
            technical_applications: data.tech_practical_applications?.technical_applications ?
                `Array with length ${data.tech_practical_applications.technical_applications.length}` : 'Missing or empty',
            technical_applications_type: data.tech_practical_applications?.technical_applications ?
                typeof data.tech_practical_applications.technical_applications : 'N/A',
            all_keys: Object.keys(data)
        });

         if (data.tech_risk_assessment?.risk_assessment?.risks?.length) {
            tabs.push({ value: 'risks', label: 'Risks', icon: <ShieldAlert className="h-4 w-4 mr-2 text-red-500" />, component: <RiskMitigationStrategies risks={data.tech_risk_assessment.risk_assessment.risks} /> });
        }
        // Tech action items are direct array
        const actionItems = data.tech_action_items?.action_items;
        if (actionItems?.length) {
            tabs.push({ value: 'action-items', label: 'Action Items', icon: <ListChecks className="h-4 w-4 mr-2 text-orange-500" />, component: <ActionItemsViewer items={actionItems} type={SOPDocument.type} /> });
        }
        if (data.tech_conclusions?.conclusions && Object.keys(data.tech_conclusions.conclusions).length > 0) {
            if (!tabs.some(tab => tab.value === 'conclusion')) {
                 tabs.push({ value: 'conclusion', label: 'Conclusion', icon: <BookText className="h-4 w-4 mr-2 text-indigo-500" />, component: <Conclusion conclusions={data.tech_conclusions.conclusions} /> });
            }
        }
        const roadmapPhases = data.tech_transformation_roadmap?.transformation_roadmap?.phases;
        if (roadmapPhases?.length) {
            tabs.push({ value: 'roadmap', label: 'Roadmap', icon: <Map className="h-4 w-4 mr-2 text-cyan-500" />, component: <RoadmapViewer phases={roadmapPhases} type={SOPDocument.type} /> });
        }
        // Tech best practices are direct array
        const bestPractices = data.tech_best_practices?.practices;
        if (bestPractices?.length) {
            tabs.push({ value: 'best-practices', label: 'Best Practices', icon: <CheckSquare className="h-4 w-4 mr-2 text-emerald-500" />, component: <BestPracticesViewer practices={bestPractices} type={SOPDocument.type} /> });
        }

        // Check for both possible structures: technical_applications or practical_applications
        const practicalApps = data.tech_practical_applications?.technical_applications ||
                             data.tech_practical_applications?.practical_applications;

        console.log('Practical Applications Debug:', {
            practicalAppsValue: practicalApps,
            practicalAppsType: typeof practicalApps,
            hasLength: practicalApps ? 'length' in practicalApps : false,
            lengthValue: practicalApps?.length,
            lengthCheck: !!practicalApps?.length,
            rawData: JSON.stringify(data.tech_practical_applications),
            hasTechnicalApplications: !!data.tech_practical_applications?.technical_applications,
            hasPracticalApplications: !!data.tech_practical_applications?.practical_applications
        });

        if (practicalApps?.length) {
            console.log('Adding practical applications tab with data:', practicalApps);
            tabs.push({ value: 'practical-applications', label: 'Applications', icon: <Lightbulb className="h-4 w-4 mr-2 text-amber-400" />, component: <PracticalApplicationsViewer type="technical" pipeline_outputs={pipelineData} />
            });
        } else {
            console.log('Not adding practical applications tab - condition failed');
        }

        // Handle both possible structures for tech resources
        let techResources: any[] | undefined = undefined;

        // Check if it's the nested structure
        if (data.tech_resources && 'tech_resources' in data.tech_resources) {
            techResources = data.tech_resources.tech_resources.resources;
        }
        // Check if it's the direct structure
        else if (data.tech_resources) {
            techResources = data.tech_resources.resources;
        }

        // Debug resources data
        console.log('Technical resources debug:', {
            techResourcesObject: data.tech_resources,
            isNestedStructure: data.tech_resources && 'tech_resources' in data.tech_resources,
            resourcesArray: techResources,
            hasLength: !!techResources?.length,
            rawData: JSON.stringify(data.tech_resources)
        });

        if (techResources?.length) {
             tabs.push({ value: 'resources', label: 'Resources', icon: <Paperclip className="h-4 w-4 mr-2 text-gray-400" />, component: <ResourceViewer resources={techResources} type={SOPDocument.type} /> });
        }
    }

    // AI Insights tab removed as it's now a button in the top right

    // Set default active tab if current one is invalid
    // if (tabs.length > 0 && !tabs.some(tab => tab.value === activeTab)) {
    //     // Use setTimeout to avoid state update during render
    //     setTimeout(() => setActiveTab(tabs[0].value), 0);
    // }

    return tabs;
    // Only SOPDocument affects tab generation
    }, [SOPDocument]);

    useEffect(() => {
        if (tabsConfig.length > 0 && !tabsConfig.some(tab => tab.value === activeTab)) {
          setActiveTab(tabsConfig[0].value);
        }
      }, [tabsConfig, activeTab]);


  // --- Render Logic ---
  if (!SOPDocument) {
    console.error('DocumentViewer: SOPDocument is null or undefined');
    return (
        <Card className="m-4 md:m-8">
            <CardHeader>
                <CardTitle>Document Not Found</CardTitle>
                <CardDescription>The requested document could not be found or loaded.</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center items-center py-16">
                <AlertCircle className="h-12 w-12 text-destructive" />
            </CardContent>
        </Card>
    );
  }

  if (!SOPDocument.type) {
    console.error('DocumentViewer: Missing document type', SOPDocument);
    return (
        <Card className="m-4 md:m-8">
            <CardHeader>
                <CardTitle>Invalid Document Type</CardTitle>
                <CardDescription>The document has a missing or invalid type.</CardDescription>
            </CardHeader>
            <CardContent className="flex flex-col gap-4 justify-center items-center py-16">
                <AlertCircle className="h-12 w-12 text-destructive" />
                <pre className="text-xs text-muted-foreground bg-muted p-4 rounded-md overflow-auto max-w-full">
                  {JSON.stringify({
                    id: SOPDocument.id,
                    type: SOPDocument.type,
                    created_at: SOPDocument.created_at,
                    metadata_title: SOPDocument.metadata?.metadata?.title || 'No title'
                  }, null, 2)}
                </pre>
            </CardContent>
        </Card>
    );
  }

  if (!SOPDocument.pipeline_outputs || typeof SOPDocument.pipeline_outputs !== 'object') {
    console.error('DocumentViewer: Invalid pipeline_outputs', SOPDocument);
    return (
        <Card className="m-4 md:m-8">
            <CardHeader>
                <CardTitle>Invalid Document Structure</CardTitle>
                <CardDescription>The document has an invalid structure or missing content.</CardDescription>
            </CardHeader>
            <CardContent className="flex flex-col gap-4 justify-center items-center py-16">
                <AlertCircle className="h-12 w-12 text-destructive" />
                <pre className="text-xs text-muted-foreground bg-muted p-4 rounded-md overflow-auto max-w-full">
                  {JSON.stringify({
                    id: SOPDocument.id,
                    type: SOPDocument.type,
                    created_at: SOPDocument.created_at,
                    has_pipeline_outputs: !!SOPDocument.pipeline_outputs,
                    pipeline_outputs_type: typeof SOPDocument.pipeline_outputs
                  }, null, 2)}
                </pre>
            </CardContent>
        </Card>
    );
  }

  // Filter out tabs with null components (e.g., if data is missing for a section)
  const validTabs = tabsConfig.filter(tab => tab.component !== null);

  console.log('DocumentViewer: Document structure', {
    id: SOPDocument.id,
    type: SOPDocument.type,
    title: SOPDocument.metadata?.metadata?.title,
    tabsConfigLength: tabsConfig.length,
    validTabsLength: validTabs.length,
    validTabValues: validTabs.map(tab => tab.value),
    pipelineOutputsKeys: Object.keys(SOPDocument.pipeline_outputs || {})
  });

  return (
    <TooltipProvider>
      <div className="container mx-auto px-4 pb-8">
         {/* Header with Back Button, Breadcrumbs, and Action Buttons */}
         <div className="flex flex-col sm:flex-row justify-between items-stretch sm:items-center gap-3 sm:gap-4 mb-4 no-print">
           <div className="flex flex-col sm:flex-row items-start gap-2 flex-shrink-0 w-full sm:w-auto">
             <BackButton
               variant="ghost"
               size="sm"
               className="w-full sm:w-auto justify-start h-full"
               fallbackUrl={`/category/${SOPDocument.mainCategorySlug}`}
               fallbackLabel={`Back to ${SOPDocument.mainCategory}`}
             />
             <Breadcrumb
               containerClassName="mt-2 sm:mt-0"
               documentTitle={SOPDocument.metadata?.metadata?.title || 'Untitled Document'}
             />
           </div>

           {/* Action Buttons in Top Right */}
           <div className="flex flex-wrap items-center gap-2 w-full sm:w-auto justify-end">
             {/* AI Insights Button removed */}

             {/* Share Button */}
             <div className="flex-shrink-0 flex-1 sm:flex-none">
               <Button
                 variant="outline"
                 size="sm"
                 className="whitespace-nowrap w-full sm:w-auto h-9 gap-2 font-medium hover:bg-primary hover:text-primary-foreground transition-all duration-200"
                 onClick={() => {
                   if (navigator.share) {
                     navigator.share({
                       title: SOPDocument.metadata?.metadata?.title || 'Document',
                       url: window.location.href,
                     });
                   } else {
                     navigator.clipboard.writeText(window.location.href);
                   }
                 }}
               >
                 <Share2 className="h-4 w-4" />
                 <span className="hidden sm:inline">Share</span>
               </Button>
             </div>
           </div>
        </div>

        <Card className="overflow-hidden mb-8 shadow-lg border-border/60 bg-card/95 backdrop-blur-sm">
          <div className="print-content p-6 sm:p-8 lg:p-12">
            {/* Pass required props to DocumentHeader */}
            <DocumentHeader
                title={SOPDocument.metadata?.metadata?.title || 'Untitled Document'}
                createdAt={SOPDocument.created_at}
                updatedAt={SOPDocument.updated_at}
                type={SOPDocument.type}
                category={SOPDocument.mainCategorySlug || 'technology'}
                subCategory={SOPDocument.subCategory}
            />

            <Separator className="my-8" />

            {/* Render Tabs only if there are valid tabs */}
            {validTabs.length > 0 ? (
                 <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                     {/* Tabs navigation container with improved layout */}
                     <div className="overflow-x-auto no-scrollbar pb-2">
                         <div className="min-w-max">
                             <TabsList className="flex flex-wrap sm:grid sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-1.5 h-auto mb-6 no-print tabs-list bg-muted/50 p-1.5 rounded-lg">
                                 {validTabs.map((tab) => (
                                     <TabsTrigger
                                         key={tab.value}
                                         value={tab.value}
                                         className="flex items-center justify-center h-9 py-1.5 px-3 text-xs sm:text-sm flex-1 sm:flex-none data-[state=active]:bg-background data-[state=active]:shadow-sm"
                                     >
                                         <span className="flex items-center justify-center">
                                             {tab.icon}
                                             <span className="truncate ml-1.5 max-w-[100px]">{tab.label}</span>
                                         </span>
                                     </TabsTrigger>
                                 ))}
                             </TabsList>
                         </div>
                     </div>

                     {/* Content area with more spacing and organization */}
                     <div className="tabs-content mt-6">
                        {validTabs.map((tab) => (
                             <TabsContent key={tab.value} value={tab.value} className="mt-6 focus-visible:outline-none focus-visible:ring-0">
                                 <div className="animate-fade-in">
                                   {tab.component}
                                 </div>
                             </TabsContent>
                         ))}
                     </div>
                 </Tabs>
            ) : (
                 <p className="text-muted-foreground text-center py-8">No content sections found in this document.</p>
            )}
          </div>
        </Card>

        {/* AI Insights Modal removed */}
    </div>
    </TooltipProvider>
  );
};

// Export as default or named, depending on usage in parent page
export default DocumentViewer;
