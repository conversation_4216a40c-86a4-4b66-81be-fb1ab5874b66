'use client';

import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ExternalLink, DollarSign, Type, BookOpen } from 'lucide-react';
import Link from 'next/link';

// Define interfaces based on JSON structure
interface NonTechResource {
  name: string;
  description: string;
  type: string; // e.g., Guide, Training
  link?: string;
  estimated_cost?: string;
  resource_id?: string;
}

interface TechResource {
  name: string;
  url: string;
  usage: string;
}

type Resource = NonTechResource | TechResource;

interface ResourceViewerProps {
  resources: Resource[];
  type: 'technical' | 'non-technical';
}

export const ResourceViewer: React.FC<ResourceViewerProps> = ({ resources, type }) => {
  if (!resources || resources.length === 0) {
    return <p className="text-muted-foreground">No resources defined.</p>;
  }

  // Function to check if a resource is a technical resource
  const isTechResource = (resource: Resource): resource is TechResource => {
    return type === 'technical' && 'url' in resource && 'usage' in resource;
  };

  // Function to check if a resource is a non-technical resource
  const isNonTechResource = (resource: Resource): resource is NonTechResource => {
    return type === 'non-technical' && 'description' in resource && 'type' in resource;
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {resources.map((resource, index) => (
        <Card key={index}>
          <CardContent className="pt-6 space-y-2">
            <h4 className="font-semibold flex items-center">
                <BookOpen className="h-4 w-4 mr-2 text-gray-400" />
                {resource.name}
            </h4>

            {/* Render based on resource type */}
            {isTechResource(resource) && (
              <>
                <p className="text-sm text-muted-foreground">{resource.usage}</p>
                <div className="flex flex-wrap gap-2 pt-2 text-xs">
                  {resource.url && (
                    <Link href={resource.url} target="_blank" rel="noopener noreferrer">
                      <Badge className="flex items-center bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-200 dark:hover:bg-blue-800">
                        <ExternalLink className="h-3 w-3 mr-1 text-blue-500" /> Visit Link
                      </Badge>
                    </Link>
                  )}
                </div>
              </>
            )}

            {isNonTechResource(resource) && (
              <>
                <p className="text-sm text-muted-foreground">{resource.description}</p>
                <div className="flex flex-wrap gap-2 pt-2 text-xs">
                  <Badge variant="outline" className="flex items-center">
                    <Type className="h-3 w-3 mr-1 text-gray-400" /> {resource.type}
                  </Badge>
                  {resource.estimated_cost && (
                    <Badge variant="outline" className="flex items-center">
                      <DollarSign className="h-3 w-3 mr-1 text-gray-400" /> {resource.estimated_cost}
                    </Badge>
                  )}
                  {resource.link && (
                    <Link href={resource.link} target="_blank" rel="noopener noreferrer">
                      <Badge className="flex items-center bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-200 dark:hover:bg-blue-800">
                        <ExternalLink className="h-3 w-3 mr-1 text-blue-500" /> Visit Link
                      </Badge>
                    </Link>
                  )}
                </div>
              </>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
};