'use client';

import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Zap } from 'lucide-react';

interface KeyTakeawaysViewerProps {
  takeaways: string[];
}

export const KeyTakeawaysViewer: React.FC<KeyTakeawaysViewerProps> = ({ takeaways }) => {
  if (!takeaways || takeaways.length === 0) {
    return <p className="text-muted-foreground">No key takeaways listed.</p>;
  }

  return (
    <Card className="border-none shadow-none">
      <CardContent className="pt-0">
        <ul className="space-y-2">
          {takeaways.map((takeaway, index) => (
            <li key={index} className="flex items-start text-sm">
              <Zap className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0 text-purple-500" />
              <span className="text-muted-foreground">{takeaway}</span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
}; 