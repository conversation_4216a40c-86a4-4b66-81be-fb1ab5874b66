'use client';

import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Info } from 'lucide-react';

interface ContextSummaryViewerProps {
  context_summary: string;
}

export const ContextSummaryViewer: React.FC<ContextSummaryViewerProps> = ({ context_summary }) => {
  if (!context_summary) {
    return <p className="text-muted-foreground">No context summary provided.</p>;
  }

  return (
    <Card className="border-l-4 border-blue-500 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-700 shadow-none">
      <CardContent className="pt-4">
        <div className="flex items-start">
          <Info className="h-5 w-5 mr-3 mt-0.5 flex-shrink-0 text-blue-600 dark:text-blue-400" />
          <p className="text-sm text-blue-800 dark:text-blue-200">{context_summary}</p>
        </div>
      </CardContent>
    </Card>
  );
}; 