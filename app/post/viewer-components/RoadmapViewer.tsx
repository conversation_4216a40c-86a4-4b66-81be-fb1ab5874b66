'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Map, Info, ListOrdered } from 'lucide-react';

// Define interfaces based on JSON structure
interface Phase {
  phase: string;
  description: string;
  steps: string[];
}

interface RoadmapViewerProps {
  phases: Phase[];
  type: 'technical' | 'non-technical'; // To potentially adjust styling/content if needed
}

export const RoadmapViewer: React.FC<RoadmapViewerProps> = ({ phases, type }) => {
  if (!phases || phases.length === 0) {
    return <p className="text-muted-foreground">No roadmap phases defined.</p>;
  }

  return (
    <Card className="border-none shadow-none">
      <CardContent className="pt-0">
        <Accordion type="single" collapsible className="w-full" defaultValue="phase-0">
          {phases.map((phase, index) => (
            <AccordionItem value={`phase-${index}`} key={phase.phase || index}>
              <AccordionTrigger className="text-left hover:no-underline">
                  <div className="flex items-start gap-2">
                      <Map className="h-4 w-4 mt-1 flex-shrink-0 text-cyan-500" />
                      <span className="flex-1 font-semibold">
                        {phase.phase}
                        {phase.description && <p className="text-sm font-normal text-muted-foreground mt-1">{phase.description}</p>}
                      </span>
                  </div>
              </AccordionTrigger>
              <AccordionContent className="pl-8 text-sm">
                {phase.steps && phase.steps.length > 0 && (
                  <div className="mb-3">
                      <h5 className="font-semibold mb-1 flex items-center text-sm"><ListOrdered className="h-4 w-4 mr-2 text-gray-400"/> Steps</h5>
                      <ul className="list-decimal list-outside pl-5 space-y-1 text-muted-foreground">
                          {phase.steps.map((step, stepIndex) => (
                              <li key={stepIndex}>{step}</li>
                          ))}
                      </ul>
                  </div>
                )}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </CardContent>
    </Card>
  );
};