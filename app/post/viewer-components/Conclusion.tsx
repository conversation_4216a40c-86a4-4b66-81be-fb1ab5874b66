'use client';

import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { BookCheck, Forward, CheckCircle } from 'lucide-react';

// Define interfaces based on the observed structure in JSON files
interface ConclusionData {
  key_findings?: string[];
  success_factors?: string[];
  next_steps?: string[];
}

interface ConclusionProps {
  conclusions: ConclusionData;
}

export const Conclusion: React.FC<ConclusionProps> = ({ conclusions }) => {
  const renderList = (title: string, items?: string[], icon?: React.ReactNode) => {
    if (!items || items.length === 0) return null;
    return (
      <div>
        <h4 className="font-semibold text-md mb-2 flex items-center">
             {icon && React.cloneElement(icon as React.ReactElement<any>, {
               className: title === "Key Findings" ? "h-4 w-4 mr-2 text-indigo-500" :
                         title === "Success Factors" ? "h-4 w-4 mr-2 text-green-500" :
                         title === "Next Steps" ? "h-4 w-4 mr-2 text-blue-500" :
                         "h-4 w-4 mr-2 text-indigo-500"
             })}
             {title}
        </h4>
        <ul className="list-disc list-outside pl-5 space-y-1 text-sm text-muted-foreground">
          {items.map((item, index) => (
            <li key={index}>{item}</li>
          ))}
        </ul>
      </div>
    );
  };

  return (
    <Card className="border-none shadow-none">
      <CardContent className="pt-0 space-y-4">
        {renderList('Key Findings', conclusions.key_findings, <BookCheck />)}
        {renderList('Success Factors', conclusions.success_factors, <CheckCircle />)}
        {renderList('Next Steps', conclusions.next_steps, <Forward />)}
      </CardContent>
    </Card>
  );
};