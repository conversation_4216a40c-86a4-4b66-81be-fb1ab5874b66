'use client';

import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { ShieldCheck, ShieldAlert } from 'lucide-react';

// Define interfaces based on JSON structure
interface RiskItem {
  id?: string;         // non-technical
  risk_id?: string;    // technical
  description: string;
  mitigation: string;
  contingency: string;
}

interface RiskMitigationStrategiesProps {
  risks: RiskItem[];
}

export const RiskMitigationStrategies: React.FC<RiskMitigationStrategiesProps> = ({ risks }) => {
  if (!risks || risks.length === 0) {
    return <p className="text-muted-foreground">No risks identified.</p>;
  }

  return (
    <Card className="border-none shadow-none">
      <CardContent className="pt-0">
        <Accordion type="single" collapsible className="w-full" defaultValue="risk-0">
          {risks.map((risk, index) => (
            <AccordionItem 
              value={`risk-${index}`} 
              key={risk.id || risk.risk_id || index}
              className="border-b border-border/40 last:border-0"
            >
              <AccordionTrigger className="text-left hover:no-underline py-4">
                <div className="flex items-start gap-2">
                  <ShieldAlert className="h-4 w-4 mt-1 flex-shrink-0 text-destructive" />
                  <span className="flex-1 font-semibold">{risk.description}</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4 pl-8 pb-4 text-sm">
                <div className="p-3 rounded-md bg-muted/40">
                  <h5 className="font-semibold mb-2 flex items-center">
                    <ShieldCheck className="h-4 w-4 mr-2 text-primary"/> 
                    Mitigation
                  </h5>
                  <p className="text-muted-foreground leading-relaxed">{risk.mitigation}</p>
                </div>
                <div className="p-3 rounded-md bg-muted/40">
                  <h5 className="font-semibold mb-2 flex items-center">
                    <ShieldAlert className="h-4 w-4 mr-2 text-orange-500" /> 
                    Contingency
                  </h5>
                  <p className="text-muted-foreground leading-relaxed">{risk.contingency}</p>
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </CardContent>
    </Card>
  );
};