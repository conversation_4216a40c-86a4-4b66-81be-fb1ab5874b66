'use client';

import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Smile, Users } from 'lucide-react';

// Define interface based on JSON structure
interface ImpactData {
  emotional_effects?: string[];
  social_consequences?: string[];
}

interface EmotionalSocialImpactViewerProps {
  impact: ImpactData;
}

export const EmotionalSocialImpactViewer: React.FC<EmotionalSocialImpactViewerProps> = ({ impact }) => {
  const renderList = (title: string, items?: string[], icon?: React.ReactNode) => {
    if (!items || items.length === 0) return null;
    return (
      <div>
        <h4 className="font-semibold text-md mb-2 flex items-center">
             {icon && React.cloneElement(icon as React.ReactElement, { className: "h-4 w-4 mr-2 opacity-80" })}
             {title}
        </h4>
        <ul className="list-disc list-outside pl-5 space-y-1 text-sm text-muted-foreground">
          {items.map((item, index) => (
            <li key={index}>{item}</li>
          ))}
        </ul>
      </div>
    );
  };

  if (!impact || (!impact.emotional_effects?.length && !impact.social_consequences?.length)) {
      return <p className="text-muted-foreground">No emotional or social impact details provided.</p>;
  }

  return (
    <Card className="border-none shadow-none">
      <CardContent className="pt-0 space-y-4">
        {renderList('Emotional Effects', impact.emotional_effects, <Smile />)}
        {renderList('Social Consequences', impact.social_consequences, <Users />)}
      </CardContent>
    </Card>
  );
}; 