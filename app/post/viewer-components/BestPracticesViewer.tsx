'use client';

import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Info, CheckCircle, Rows, Target, Settings, HardHat } from 'lucide-react';

// Define interfaces based on JSON structure (slightly different between tech/non-tech)
interface Practice {
  practice: string;
  explanation?: string; // Common
  expected_outcomes?: string; // Common
  implementation_guide?: string; // Common
  resource_requirements?: string; // Common
  variations?: string[]; // Common
  expertise_level?: string; // non-technical
}

interface BestPracticesViewerProps {
  practices: Practice[];
  type: 'technical' | 'non-technical';
}

export const BestPracticesViewer: React.FC<BestPracticesViewerProps> = ({ practices, type }) => {
  if (!practices || practices.length === 0) {
    return <p className="text-muted-foreground">No best practices defined.</p>;
  }

  return (
    <Card className="border-none shadow-none">
      <CardContent className="pt-0">
        <Accordion type="single" collapsible className="w-full" defaultValue="practice-0">
          {practices.map((practice, index) => (
            <AccordionItem 
              value={`practice-${index}`} 
              key={practice.practice || index}
              className="border-b border-border/40 last:border-0"
            >
              <AccordionTrigger className="text-left hover:no-underline py-4">
                <div className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 mt-1 flex-shrink-0 text-emerald-500" />
                  <span className="flex-1 font-semibold">{practice.practice}</span>
                </div>
              </AccordionTrigger>
              
              <AccordionContent className="pl-8 pb-4 text-sm space-y-4">
                {practice.explanation && (
                  <p className="text-muted-foreground italic leading-relaxed">{practice.explanation}</p>
                )}

                {practice.variations && practice.variations.length > 0 && (
                  <div className="p-3 rounded-md bg-muted/40">
                    <h5 className="font-semibold mb-2 flex items-center text-sm">
                      <Rows className="h-4 w-4 mr-2 text-teal-500"/> 
                      Variations
                    </h5>
                    <ul className="list-disc list-outside pl-5 space-y-1.5 text-muted-foreground">
                      {practice.variations.map((variation, vIndex) => (
                        <li key={vIndex} className="leading-relaxed">{variation}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {practice.expected_outcomes && (
                  <div className="p-3 rounded-md bg-muted/40">
                    <h5 className="font-semibold mb-2 flex items-center text-sm">
                      <Target className="h-4 w-4 mr-2 text-green-500"/> 
                      Expected Outcomes
                    </h5>
                    <p className="text-muted-foreground leading-relaxed">{practice.expected_outcomes}</p>
                  </div>
                )}

                {practice.implementation_guide && (
                  <div className="p-3 rounded-md bg-muted/40">
                    <h5 className="font-semibold mb-2 flex items-center text-sm">
                      <Settings className="h-4 w-4 mr-2 text-gray-400"/> 
                      Implementation Guide
                    </h5>
                    <p className="text-muted-foreground leading-relaxed">{practice.implementation_guide}</p>
                  </div>
                )}

                {practice.resource_requirements && (
                  <div className="p-3 rounded-md bg-muted/40">
                    <h5 className="font-semibold mb-2 flex items-center text-sm">
                      <HardHat className="h-4 w-4 mr-2 text-cyan-400"/> 
                      Resource Requirements
                    </h5>
                    <p className="text-muted-foreground leading-relaxed">{practice.resource_requirements}</p>
                  </div>
                )}

                {practice.expertise_level && (
                  <div className="flex items-center gap-2 p-3">
                    <h5 className="font-semibold text-sm">Expertise Level:</h5>
                    <Badge variant="secondary">{practice.expertise_level}</Badge>
                  </div>
                )}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </CardContent>
    </Card>
  );
};