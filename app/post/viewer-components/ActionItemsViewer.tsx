'use client';

import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { CheckSquare, Library, GitBranch, CheckCircle2 } from 'lucide-react';

// Define interfaces based on JSON structure (slightly different keys)
interface ActionItem {
  id: string; // Common ID
  description: string;
  dependencies: string[];
  requirements: string[];
  acceptance_criteria?: string[]; // non-technical only
}

// Nested structure in non-technical JSON
interface NonTechActionItemsWrapper {
    action_items: ActionItem[];
}

interface ActionItemsViewerProps {
  // Accept either the direct array (technical) or the wrapped object (non-technical)
  items: ActionItem[] | NonTechActionItemsWrapper;
  type: "technical" | "non-technical";
}

export const ActionItemsViewer: React.FC<ActionItemsViewerProps> = ({ items, type }) => {
  // Normalize the input to always be an array
  const actionItems = Array.isArray(items) ? items : items?.action_items;

  if (!actionItems || actionItems.length === 0) {
    return <p className="text-muted-foreground">No action items defined.</p>;
  }

  return (
    <Card className="border-none shadow-none">
      <CardContent className="pt-0">
        <Accordion type="single" collapsible className="w-full" defaultValue="action-0">
          {actionItems.map((item, index) => (
            <AccordionItem 
              value={`action-${index}`} 
              key={item.id || index}
              className="border-b border-border/40 last:border-0"
            >
              <AccordionTrigger className="text-left hover:no-underline py-4">
                <div className="flex items-start gap-2">
                  <CheckSquare className="h-4 w-4 mt-1 flex-shrink-0 text-orange-500" />
                  <span className="flex-1 font-semibold">{item.description}</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="pl-8 pb-4 text-sm space-y-4">
                {item.dependencies && item.dependencies.length > 0 && (
                  <div className="p-3 rounded-md bg-muted/40">
                    <h5 className="font-semibold mb-2 flex items-center text-sm">
                      <GitBranch className="h-4 w-4 mr-2 text-blue-400"/> 
                      Dependencies
                    </h5>
                    <ul className="list-disc list-outside pl-5 space-y-1.5 text-muted-foreground">
                      {item.dependencies.map((dep, depIndex) => (
                        <li key={depIndex} className="leading-relaxed">{dep}</li>
                      ))}
                    </ul>
                  </div>
                )}
                {item.requirements && item.requirements.length > 0 && (
                  <div className="p-3 rounded-md bg-muted/40">
                    <h5 className="font-semibold mb-2 flex items-center text-sm">
                      <Library className="h-4 w-4 mr-2 text-indigo-400"/> 
                      Requirements
                    </h5>
                    <ul className="list-disc list-outside pl-5 space-y-1.5 text-muted-foreground">
                      {item.requirements.map((req, reqIndex) => (
                        <li key={reqIndex} className="leading-relaxed">{req}</li>
                      ))}
                    </ul>
                  </div>
                )}
                {item.acceptance_criteria && item.acceptance_criteria.length > 0 && (
                  <div className="p-3 rounded-md bg-muted/40">
                    <h5 className="font-semibold mb-2 flex items-center text-sm">
                      <CheckCircle2 className="h-4 w-4 mr-2 text-green-500"/> 
                      Acceptance Criteria
                    </h5>
                    <ul className="list-disc list-outside pl-5 space-y-1.5 text-muted-foreground">
                      {item.acceptance_criteria.map((ac, acIndex) => (
                        <li key={acIndex} className="leading-relaxed">{ac}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </CardContent>
    </Card>
  );
};