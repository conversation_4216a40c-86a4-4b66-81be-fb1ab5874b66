'use client';

import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Lightbulb } from 'lucide-react';

interface PracticalApplicationsViewerProps {
  type: 'technical' | 'non-technical';
  pipeline_outputs: Record<string, any>;
}

export const PracticalApplicationsViewer: React.FC<PracticalApplicationsViewerProps> = ({ type, pipeline_outputs }) => {
  let applications: string[] = [];

  console.log('PracticalApplicationsViewer received:', {
    type,
    pipeline_outputs_keys: Object.keys(pipeline_outputs || {}),
    tech_practical_applications: pipeline_outputs?.tech_practical_applications ?
      JSON.stringify(pipeline_outputs.tech_practical_applications) : 'Missing',
    biz_practical_applications: pipeline_outputs?.biz_practical_applications ?
      JSON.stringify(pipeline_outputs.biz_practical_applications) : 'Missing'
  });

  if (type === 'technical') {
    console.log('Technical applications extraction:', {
      tech_practical_applications: pipeline_outputs?.tech_practical_applications ?
        typeof pipeline_outputs.tech_practical_applications : 'Missing',
      technical_applications: pipeline_outputs?.tech_practical_applications?.technical_applications ?
        `Array with length ${pipeline_outputs.tech_practical_applications.technical_applications.length}` : 'Missing or empty',
      practical_applications: pipeline_outputs?.tech_practical_applications?.practical_applications ?
        `Array with length ${pipeline_outputs.tech_practical_applications.practical_applications.length}` : 'Missing or empty',
      technical_applications_type: pipeline_outputs?.tech_practical_applications?.technical_applications ?
        typeof pipeline_outputs.tech_practical_applications.technical_applications : 'N/A',
      practical_applications_type: pipeline_outputs?.tech_practical_applications?.practical_applications ?
        typeof pipeline_outputs.tech_practical_applications.practical_applications : 'N/A'
    });
    // Try to get applications from either technical_applications or practical_applications
    applications = pipeline_outputs?.tech_practical_applications?.technical_applications ||
                  pipeline_outputs?.tech_practical_applications?.practical_applications || [];
  } else if (type === 'non-technical') {
    console.log('Non-technical applications extraction:', {
      biz_practical_applications: pipeline_outputs?.biz_practical_applications ?
        typeof pipeline_outputs.biz_practical_applications : 'Missing',
      practical_applications: pipeline_outputs?.biz_practical_applications?.practical_applications ?
        `Array with length ${pipeline_outputs.biz_practical_applications.practical_applications.length}` : 'Missing or empty',
      practical_applications_type: pipeline_outputs?.biz_practical_applications?.practical_applications ?
        typeof pipeline_outputs.biz_practical_applications.practical_applications : 'N/A'
    });
    applications = pipeline_outputs?.biz_practical_applications?.practical_applications || [];
  }

  console.log('Final applications array:', {
    hasApplications: !!applications,
    applicationsLength: applications?.length,
    applicationsType: typeof applications,
    isArray: Array.isArray(applications),
    applications: applications
  });

  if (!applications || applications.length === 0) {
    console.log('No applications to display, returning empty message');
    return <p className="text-muted-foreground">No practical applications listed.</p>;
  }

  return (
    <Card className="border-none shadow-none">
      <CardContent className="pt-0">
        <ul className="space-y-2">
          {applications.map((app, index) => (
            <li key={index} className="flex items-start text-sm">
              <Lightbulb className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0 text-yellow-500" />
              <span className="text-muted-foreground">{app}</span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
};
