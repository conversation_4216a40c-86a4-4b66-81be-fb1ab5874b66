'use client';

import React from 'react';
import { Calendar, Tag } from 'lucide-react';
import { CategoryBadge } from '@/components/category-badge';

interface DocumentHeaderProps {
  title: string;
  createdAt: string | Date;
  updatedAt: string | Date;
  type: 'technical' | 'non-technical';
  category?: string;
  subCategory?: string;
}

export const DocumentHeader: React.FC<DocumentHeaderProps> = ({
  title,
  createdAt,
  updatedAt,
  type,
  category = 'technology', // Default to technology if no category is provided
  subCategory
}) => {
  const formatDate = (dateString: string | Date) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric', month: 'long', day: 'numeric'
    });
  };

  return (
    <div className="mb-4 sm:mb-6">
      <h1 className="text-xl sm:text-2xl md:text-3xl font-bold tracking-tight mb-3 break-words leading-tight">{title}</h1>
      <div className="flex flex-wrap items-center gap-x-3 sm:gap-x-4 gap-y-2 text-xs sm:text-sm text-muted-foreground">
        <div className="flex items-center flex-shrink-0">
          <Tag className="mr-1.5 h-3.5 sm:h-4 w-3.5 sm:w-4 flex-shrink-0" />
          <CategoryBadge
            category={category}
            subCategory={subCategory}
            className="capitalize text-xs"
            showSparkle={true}
          >
            {type} SOP
          </CategoryBadge>
        </div>
        <div className="flex items-center flex-shrink-0">
          <Calendar className="mr-1.5 h-3.5 sm:h-4 w-3.5 sm:w-4 flex-shrink-0" />
          <span className="whitespace-nowrap">Created: {formatDate(createdAt)}</span>
        </div>
        <div className="flex items-center flex-shrink-0">
          <Calendar className="mr-1.5 h-3.5 sm:h-4 w-3.5 sm:w-4 flex-shrink-0" />
          <span className="whitespace-nowrap">Updated: {formatDate(updatedAt)}</span>
        </div>
      </div>
    </div>
  );
};