'use client';

import React from 'react';
import { Calendar, Tag } from 'lucide-react';
import { CategoryBadge } from '@/components/category-badge';

interface DocumentHeaderProps {
  title: string;
  createdAt: string | Date;
  updatedAt: string | Date;
  type: 'technical' | 'non-technical';
  category?: string;
  subCategory?: string;
}

export const DocumentHeader: React.FC<DocumentHeaderProps> = ({
  title,
  createdAt,
  updatedAt,
  type,
  category = 'technology', // Default to technology if no category is provided
  subCategory
}) => {
  const formatDate = (dateString: string | Date) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric', month: 'long', day: 'numeric'
    });
  };

  return (
    <div className="mb-8 sm:mb-10">
      <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold tracking-tight mb-4 break-words leading-tight text-foreground">{title}</h1>
      <div className="flex flex-wrap items-center gap-x-4 sm:gap-x-6 gap-y-3 text-sm sm:text-base text-muted-foreground">
        <div className="flex items-center flex-shrink-0">
          <Tag className="mr-2 h-4 sm:h-5 w-4 sm:w-5 flex-shrink-0" />
          <CategoryBadge
            category={category}
            subCategory={subCategory}
            className="capitalize text-sm font-medium"
            showSparkle={true}
          >
            {type} SOP
          </CategoryBadge>
        </div>
        <div className="flex items-center flex-shrink-0">
          <Calendar className="mr-2 h-4 sm:h-5 w-4 sm:w-5 flex-shrink-0" />
          <span className="whitespace-nowrap font-medium">Created: {formatDate(createdAt)}</span>
        </div>
      </div>
    </div>
  );
};