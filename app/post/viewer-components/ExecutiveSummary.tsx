'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Info, Lightbulb, Target, AlertTriangle, Bar<PERSON>hart } from 'lucide-react';

// Define interfaces based on the observed structure in JSON files
interface ExecutiveSummaryData {
  core_insights?: string[];
  expected_outcomes?: string[];
  critical_considerations?: string[];
  strategic_recommendations?: string[];
}

interface ExecutiveSummaryProps {
  summary: ExecutiveSummaryData;
}

export const ExecutiveSummary: React.FC<ExecutiveSummaryProps> = ({ summary }) => {
  // Ensure summary is an object
  const safeSummary = summary || {};

  // Add debug logging
  console.log('ExecutiveSummary component received:', {
    summaryExists: !!summary,
    summaryType: typeof summary,
    coreInsights: safeSummary.core_insights?.length || 0,
    expectedOutcomes: safeSummary.expected_outcomes?.length || 0,
    criticalConsiderations: safeSummary.critical_considerations?.length || 0,
    strategicRecommendations: safeSummary.strategic_recommendations?.length || 0
  });

  const renderList = (title: string, items?: string[], icon?: React.ReactNode, insightTopic?: string) => {
    if (!items || items.length === 0) return null;

    // Define icon colors based on title
    let iconColor = "text-blue-500";
    let bgColor = "bg-blue-500/5 dark:bg-blue-950/40";
    let borderColor = "border-blue-200/30 dark:border-blue-800";
    
    if (title === "Core Insights") {
      iconColor = "text-amber-400";
      bgColor = "bg-amber-500/5 dark:bg-amber-950/40";
      borderColor = "border-amber-200/30 dark:border-amber-800";
    }
    if (title === "Expected Outcomes") {
      iconColor = "text-green-500";
      bgColor = "bg-green-500/5 dark:bg-green-950/40";
      borderColor = "border-green-200/30 dark:border-green-800";
    }
    if (title === "Critical Considerations") {
      iconColor = "text-red-500";
      bgColor = "bg-red-500/5 dark:bg-red-950/40";
      borderColor = "border-red-200/30 dark:border-red-800";
    }
    if (title === "Strategic Recommendations") {
      iconColor = "text-purple-500";
      bgColor = "bg-purple-500/5 dark:bg-purple-950/40";
      borderColor = "border-purple-200/30 dark:border-purple-800";
    }

    return (
      <div className={`h-full p-4 border rounded-lg ${borderColor} ${bgColor} hover:bg-card/80 dark:hover:bg-card/30 shadow-sm dark:shadow-md transition-colors`}>
        <div className="flex items-center mb-3">
           <h4 className="font-semibold text-md flex items-center">
               {icon && React.cloneElement(icon as React.ReactElement, { className: `h-4 w-4 mr-2 ${iconColor}` })}
               {title}
           </h4>
        </div>
        <ul className="list-disc list-outside pl-5 space-y-1.5 text-sm">
          {items.map((item, index) => (
            <li key={index} className="leading-relaxed text-foreground dark:text-foreground/90">{item}</li>
          ))}
        </ul>
      </div>
    );
  };

  // Check if there's any content to display
  const hasContent =
    (safeSummary.core_insights && safeSummary.core_insights.length > 0) ||
    (safeSummary.expected_outcomes && safeSummary.expected_outcomes.length > 0) ||
    (safeSummary.critical_considerations && safeSummary.critical_considerations.length > 0) ||
    (safeSummary.strategic_recommendations && safeSummary.strategic_recommendations.length > 0);

  // If no content, show a message
  if (!hasContent) {
    return (
      <Card className="border-none shadow-none bg-transparent">
        <CardContent className="pt-0">
          <p className="text-slate-400 text-sm">No executive summary information available.</p>
        </CardContent>
      </Card>
    );
  }

  // Organize sections for 2x2 grid
  const coreInsights = renderList('Core Insights', safeSummary.core_insights, <Lightbulb />, 'core_insights');
  const expectedOutcomes = renderList('Expected Outcomes', safeSummary.expected_outcomes, <Target />, 'expected_outcomes');
  const criticalConsiderations = renderList('Critical Considerations', safeSummary.critical_considerations, <AlertTriangle />, 'critical_considerations');
  const strategicRecommendations = renderList('Strategic Recommendations', safeSummary.strategic_recommendations, <BarChart />, 'strategic_recommendations');

  return (
    <Card className="border-none shadow-none bg-transparent">
      <CardContent className="pt-0">
        {/* Mobile view: stack all sections vertically */}
        <div className="md:hidden space-y-4">
          {coreInsights}
          {expectedOutcomes}
          {criticalConsiderations}
          {strategicRecommendations}
        </div>
        
        {/* Desktop view: 2x2 grid layout */}
        <div className="hidden md:grid md:grid-cols-2 gap-4">
          <div>{coreInsights}</div>
          <div>{expectedOutcomes}</div>
          <div>{criticalConsiderations}</div>
          <div>{strategicRecommendations}</div>
        </div>
      </CardContent>
    </Card>
  );
};