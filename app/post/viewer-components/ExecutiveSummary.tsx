'use client';

import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Lightbulb, Target, AlertTriangle, BarChart } from 'lucide-react';

// Define interfaces based on the observed structure in JSON files
interface ExecutiveSummaryData {
  core_insights?: string[];
  expected_outcomes?: string[];
  critical_considerations?: string[];
  strategic_recommendations?: string[];
}

interface ExecutiveSummaryProps {
  summary: ExecutiveSummaryData;
}

export const ExecutiveSummary: React.FC<ExecutiveSummaryProps> = ({ summary }) => {
  // Ensure summary is an object
  const safeSummary = summary || {};

  // Add debug logging
  console.log('ExecutiveSummary component received:', {
    summaryExists: !!summary,
    summaryType: typeof summary,
    coreInsights: safeSummary.core_insights?.length || 0,
    expectedOutcomes: safeSummary.expected_outcomes?.length || 0,
    criticalConsiderations: safeSummary.critical_considerations?.length || 0,
    strategicRecommendations: safeSummary.strategic_recommendations?.length || 0
  });

  const renderList = (title: string, items?: string[], icon?: React.ReactNode) => {
    if (!items || items.length === 0) return null;

    // Define icon colors based on title
    let iconColor = "text-blue-500";
    let borderColor = "border-blue-200/30 dark:border-blue-800";

    if (title === "Core Insights") {
      iconColor = "text-amber-400";
      borderColor = "border-amber-200/30 dark:border-amber-800";
    }
    if (title === "Expected Outcomes") {
      iconColor = "text-green-500";
      borderColor = "border-green-200/30 dark:border-green-800";
    }
    if (title === "Critical Considerations") {
      iconColor = "text-red-500";
      borderColor = "border-red-200/30 dark:border-red-800";
    }
    if (title === "Strategic Recommendations") {
      iconColor = "text-purple-500";
      borderColor = "border-purple-200/30 dark:border-purple-800";
    }

    // Add visual indicators for accessibility
    let borderStyle = "border-l-4";

    if (title === "Core Insights") {
      borderStyle = "border-l-4 border-l-amber-400";
    }
    if (title === "Expected Outcomes") {
      borderStyle = "border-l-4 border-l-green-500";
    }
    if (title === "Critical Considerations") {
      borderStyle = "border-l-4 border-l-red-500";
    }
    if (title === "Strategic Recommendations") {
      borderStyle = "border-l-4 border-l-purple-500";
    }

    return (
      <div className={`flex flex-col h-full p-4 border rounded-lg ${borderColor} bg-transparent ${borderStyle} hover:shadow-sm shadow-none transition-all duration-200 ease-in-out`}>
        <div className="flex items-center mb-3 flex-shrink-0">
           <h4 className="font-medium text-base flex items-center">
               {icon && React.cloneElement(icon as React.ReactElement, { className: `h-4 w-4 mr-2 ${iconColor}` })}
               {title}
           </h4>
        </div>
        <div className="flex-1">
          <ul className="list-disc list-outside pl-6 space-y-2 text-sm leading-relaxed">
            {items.map((item, index) => (
              <li key={index} className="text-foreground dark:text-foreground/90 leading-relaxed">{item}</li>
            ))}
          </ul>
        </div>
      </div>
    );
  };

  // Check if there's any content to display
  const hasContent =
    (safeSummary.core_insights && safeSummary.core_insights.length > 0) ||
    (safeSummary.expected_outcomes && safeSummary.expected_outcomes.length > 0) ||
    (safeSummary.critical_considerations && safeSummary.critical_considerations.length > 0) ||
    (safeSummary.strategic_recommendations && safeSummary.strategic_recommendations.length > 0);

  // If no content, show a message
  if (!hasContent) {
    return (
      <Card className="border-none shadow-none bg-transparent">
        <CardContent className="pt-0">
          <p className="text-slate-400 text-sm">No executive summary information available.</p>
        </CardContent>
      </Card>
    );
  }

  // Organize sections for 2x2 grid
  const coreInsights = renderList('Core Insights', safeSummary.core_insights, <Lightbulb />);
  const expectedOutcomes = renderList('Expected Outcomes', safeSummary.expected_outcomes, <Target />);
  const criticalConsiderations = renderList('Critical Considerations', safeSummary.critical_considerations, <AlertTriangle />);
  const strategicRecommendations = renderList('Strategic Recommendations', safeSummary.strategic_recommendations, <BarChart />);

  return (
    <Card className="border-none shadow-none bg-transparent">
      <CardContent className="pt-0">
        {/* Mobile view: stack all sections vertically */}
        <div className="md:hidden space-y-6">
          {coreInsights}
          {expectedOutcomes}
          {criticalConsiderations}
          {strategicRecommendations}
        </div>

        {/* Desktop view: 2x2 grid layout with equal heights */}
        <div className="hidden md:grid md:grid-cols-2 gap-6">
          <div className="flex flex-col h-full">{coreInsights}</div>
          <div className="flex flex-col h-full">{expectedOutcomes}</div>
          <div className="flex flex-col h-full">{criticalConsiderations}</div>
          <div className="flex flex-col h-full">{strategicRecommendations}</div>
        </div>
      </CardContent>
    </Card>
  );
};