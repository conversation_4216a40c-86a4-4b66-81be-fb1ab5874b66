import { Metadata } from 'next'
import { Mail, CheckCircle, TrendingUp, Users, Zap, Shield } from 'lucide-react'
import { NewsletterSignup } from '@/components/newsletter-signup'

export const metadata: Metadata = {
  title: 'Newsletter | DuvaInsights',
  description: 'Subscribe to DuvaInsights newsletter for weekly trend insights and analysis delivered to your inbox.',
}

const benefits = [
  {
    icon: TrendingUp,
    title: 'Curated Trends',
    description: 'Hand-picked insights from across technology, business, health, and culture.',
  },
  {
    icon: Zap,
    title: 'Weekly Digest',
    description: 'Stay informed without information overload. One email, once a week.',
  },
  {
    icon: Users,
    title: 'Join 50,000+ Readers',
    description: 'Be part of a community of forward-thinking professionals and innovators.',
  },
  {
    icon: Shield,
    title: 'Privacy First',
    description: 'No spam, ever. Unsubscribe anytime. We respect your inbox and data.',
  },
]

const testimonials = [
  {
    quote: "DuvaInsights newsletter is the first thing I read every Monday. It keeps me ahead of the curve in my industry.",
    author: '<PERSON>',
    role: 'Tech Entrepreneur',
  },
  {
    quote: "Concise, insightful, and always relevant. It's like having a team of trend analysts working for you.",
    author: '<PERSON>',
    role: 'Investment Manager',
  },
  {
    quote: "I've discovered so many emerging trends through DuvaInsights that have directly impacted my business strategy.",
    author: 'Emily Rodriguez',
    role: 'Marketing Director',
  },
]

export default function NewsletterPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-muted/50 to-background py-16 md:py-24">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-3xl text-center">
            <div className="mb-6 inline-flex items-center rounded-full bg-primary/10 px-4 py-2">
              <Mail className="mr-2 h-4 w-4 text-primary" />
              <span className="text-sm font-medium text-primary">Free Weekly Newsletter</span>
            </div>
            <h1 className="mb-6 text-4xl font-bold md:text-5xl">
              Your Weekly Dose of <span className="text-gradient">Future Insights</span>
            </h1>
            <p className="mb-8 text-lg text-muted-foreground">
              Join 50,000+ professionals who rely on DuvaInsights to stay ahead of emerging trends. 
              Get curated insights delivered to your inbox every Monday.
            </p>
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="container mx-auto px-4 py-12">
        <div className="mx-auto max-w-2xl -mt-8">
          <NewsletterSignup />
        </div>
      </section>

      {/* Benefits */}
      <section className="container mx-auto px-4 py-16">
        <div className="mx-auto max-w-4xl">
          <h2 className="mb-12 text-center text-3xl font-bold">Why Subscribe?</h2>
          <div className="grid gap-8 md:grid-cols-2">
            {benefits.map((benefit) => {
              const Icon = benefit.icon
              return (
                <div key={benefit.title} className="flex gap-4">
                  <div className="flex-shrink-0">
                    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                      <Icon className="h-6 w-6 text-primary" />
                    </div>
                  </div>
                  <div>
                    <h3 className="mb-2 text-lg font-semibold">{benefit.title}</h3>
                    <p className="text-muted-foreground">{benefit.description}</p>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Sample Content */}
      <section className="bg-muted/50 py-16">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-3xl">
            <h2 className="mb-8 text-center text-3xl font-bold">What You'll Get</h2>
            <div className="rounded-lg border bg-card p-8">
              <h3 className="mb-4 text-xl font-semibold">📧 A Typical DuvaInsights Newsletter Includes:</h3>
              <ul className="space-y-3">
                <li className="flex items-start gap-3">
                  <CheckCircle className="mt-0.5 h-5 w-5 flex-shrink-0 text-green-600" />
                  <div>
                    <strong>Trend of the Week:</strong> Deep dive into the most significant emerging trend
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <CheckCircle className="mt-0.5 h-5 w-5 flex-shrink-0 text-green-600" />
                  <div>
                    <strong>Quick Insights:</strong> 5-7 bite-sized trends across different categories
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <CheckCircle className="mt-0.5 h-5 w-5 flex-shrink-0 text-green-600" />
                  <div>
                    <strong>What to Watch:</strong> Upcoming developments and predictions
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <CheckCircle className="mt-0.5 h-5 w-5 flex-shrink-0 text-green-600" />
                  <div>
                    <strong>Resources & Tools:</strong> Curated links for deeper exploration
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <CheckCircle className="mt-0.5 h-5 w-5 flex-shrink-0 text-green-600" />
                  <div>
                    <strong>Community Spotlight:</strong> Insights from industry leaders
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="container mx-auto px-4 py-16">
        <div className="mx-auto max-w-4xl">
          <h2 className="mb-12 text-center text-3xl font-bold">What Subscribers Say</h2>
          <div className="grid gap-8 md:grid-cols-3">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="rounded-lg border bg-card p-6">
                <blockquote className="mb-4">
                  <p className="text-muted-foreground italic">"{testimonial.quote}"</p>
                </blockquote>
                <div className="text-sm">
                  <p className="font-semibold">{testimonial.author}</p>
                  <p className="text-muted-foreground">{testimonial.role}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Final CTA */}
      <section className="bg-gradient-to-t from-muted/50 to-background py-16">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="mb-4 text-3xl font-bold">Ready to Stay Ahead?</h2>
            <p className="mb-8 text-muted-foreground">
              Join thousands of professionals who start their week with DuvaInsights.
            </p>
            <NewsletterSignup />
          </div>
        </div>
      </section>
    </div>
  )
}
