'use client'

import { Target, Eye, Users, Lightbulb, TrendingUp, Globe, Building, Award, BookOpen, ArrowUpRight, Calendar, Clock } from 'lucide-react'
import Link from 'next/link'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardFooter } from '@/components/ui/card'
import { Breadcrumb } from '@/components/breadcrumb'
import { BackButton } from '@/components/back-button'

// Metadata is now defined in metadata.ts file in the same directory

const values = [
  {
    icon: Eye,
    title: 'Clarity',
    description: 'We cut through the noise to deliver clear, actionable insights that matter.',
    color: 'text-blue-500 dark:text-blue-400',
    bgColor: 'bg-blue-100 dark:bg-blue-900/20',
  },
  {
    icon: Lightbulb,
    title: 'Innovation',
    description: 'We constantly explore new ways to identify and analyze emerging trends.',
    color: 'text-amber-500 dark:text-amber-400',
    bgColor: 'bg-amber-100 dark:bg-amber-900/20',
  },
  {
    icon: Users,
    title: 'Community',
    description: 'We believe in the power of collective intelligence and shared knowledge.',
    color: 'text-green-500 dark:text-green-400',
    bgColor: 'bg-green-100 dark:bg-green-900/20',
  },
  {
    icon: Target,
    title: 'Accuracy',
    description: 'We prioritize quality over quantity, ensuring every insight is well-researched.',
    color: 'text-red-500 dark:text-red-400',
    bgColor: 'bg-red-100 dark:bg-red-900/20',
  },
]

export default function AboutPage() {
  return (
    <div className="min-h-screen animate-fade-in">
      {/* Navigation */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex flex-col sm:flex-row justify-between items-start gap-2">
          <BackButton />
          <Breadcrumb />
        </div>
      </div>

      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-b from-blue-50 via-muted/50 to-background dark:from-blue-950/20 dark:via-slate-900/30 dark:to-background py-16 md:py-24">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-3xl text-center">
            <div className="mb-6 inline-flex items-center rounded-full bg-primary/10 px-4 py-2 ring-1 ring-primary/20">
              <Building className="mr-2 h-4 w-4 text-primary animate-pulse" />
              <span className="text-sm font-medium text-primary">About DuvaInsights</span>
            </div>
            <h1 className="mb-6 text-4xl font-bold md:text-5xl">
              Helping You Navigate <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-600 dark:from-primary dark:to-blue-400">Tomorrow</span>
            </h1>
            <p className="text-lg text-muted-foreground">
              We believe everyone deserves access to high-quality trend analysis and future insights.
              Our mission is to democratize trend discovery and help professionals stay ahead of the curve.
            </p>
            <div className="mt-8 flex flex-wrap justify-center gap-4">
              <Button asChild className="gap-2 bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-600/90">
                <Link href="/trending">
                  <TrendingUp className="h-4 w-4" />
                  Explore Trends
                </Link>
              </Button>
              <Button asChild variant="outline" className="border-primary/20 hover:bg-primary/5">
                <Link href="/newsletter">
                  Subscribe to Updates
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Mission Statement */}
      <section className="container mx-auto px-4 py-16">
        <div className="mx-auto max-w-5xl">
          <div className="grid gap-8 md:grid-cols-2">
            <Card className="rounded-lg border border-blue-100 dark:border-blue-900/30 bg-card text-card-foreground shadow-sm transition-all duration-200 hover:shadow-md focus-within:ring-1 focus-within:ring-primary/40 overflow-hidden group">
              <div className="h-1 w-full bg-gradient-to-r from-blue-500 to-primary"></div>
              <CardHeader className="p-5">
                <h2 className="text-2xl font-bold group-hover:text-primary transition-colors">Our Mission</h2>
              </CardHeader>
              <CardContent className="p-5 pt-0 space-y-4">
                <p className="text-muted-foreground">
                  In an era of information overload, identifying genuine trends from noise has become increasingly challenging.
                  DuvaInsights was founded to solve this problem.
                </p>
                <p className="text-muted-foreground">
                  We combine human expertise with advanced analytics to surface the trends that truly matter.
                  Our goal is to empower professionals, entrepreneurs, and curious minds with the insights they need to make informed decisions about the future.
                </p>
                <p className="text-muted-foreground">
                  Whether you're planning business strategy, making investment decisions, or simply staying informed,
                  DuvaInsights provides the clarity and foresight you need to succeed.
                </p>
                <div className="pt-4">
                  <Link href="/trending" className="group inline-flex items-center gap-1 font-medium text-primary hover:underline">
                    <span>Discover our trending insights</span>
                    <ArrowUpRight className="h-4 w-4 transition-transform group-hover:translate-x-0.5 group-hover:-translate-y-0.5" />
                  </Link>
                </div>
              </CardContent>
              <CardFooter className="p-5 pt-0">
                <div className="flex items-center space-x-2 text-blue-500 dark:text-blue-400">
                  <Calendar className="h-4 w-4" />
                  <span className="text-sm">Updated regularly</span>
                </div>
              </CardFooter>
            </Card>

            <Card className="rounded-lg border border-purple-100 dark:border-purple-900/30 bg-card text-card-foreground shadow-sm transition-all duration-200 hover:shadow-md focus-within:ring-1 focus-within:ring-primary/40 overflow-hidden group">
              <div className="h-1 w-full bg-gradient-to-r from-purple-500 to-primary"></div>
              <CardHeader className="p-5">
                <h2 className="text-2xl font-bold group-hover:text-primary transition-colors">What Sets Us Apart</h2>
              </CardHeader>
              <CardContent className="p-5 pt-0">
                <ul className="space-y-6">
                  <li className="flex items-start gap-4 group/item hover:-translate-y-0.5 transition-transform">
                    <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/40 dark:to-blue-800/40 transition-all duration-300 group-hover/item:shadow-md">
                      <Globe className="h-5 w-5 text-blue-500 dark:text-blue-400 group-hover/item:scale-110 transition-transform" />
                    </div>
                    <div>
                      <strong className="text-lg font-medium text-blue-700 dark:text-blue-300">Global Perspective</strong>
                      <p className="mt-1 text-muted-foreground">
                        We track trends across industries and geographies for comprehensive insights
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start gap-4 group/item hover:-translate-y-0.5 transition-transform">
                    <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-gradient-to-br from-red-100 to-red-200 dark:from-red-900/40 dark:to-red-800/40 transition-all duration-300 group-hover/item:shadow-md">
                      <Target className="h-5 w-5 text-red-500 dark:text-red-400 group-hover/item:scale-110 transition-transform" />
                    </div>
                    <div>
                      <strong className="text-lg font-medium text-red-700 dark:text-red-300">Curated Quality</strong>
                      <p className="mt-1 text-muted-foreground">
                        Every trend is carefully researched and verified by domain experts
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start gap-4 group/item hover:-translate-y-0.5 transition-transform">
                    <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-gradient-to-br from-amber-100 to-amber-200 dark:from-amber-900/40 dark:to-amber-800/40 transition-all duration-300 group-hover/item:shadow-md">
                      <Lightbulb className="h-5 w-5 text-amber-500 dark:text-amber-400 group-hover/item:scale-110 transition-transform" />
                    </div>
                    <div>
                      <strong className="text-lg font-medium text-amber-700 dark:text-amber-300">Actionable Insights</strong>
                      <p className="mt-1 text-muted-foreground">
                        We don't just report trends—we explain what they mean for you
                      </p>
                    </div>
                  </li>
                </ul>
              </CardContent>
              <CardFooter className="p-5 pt-0">
                <div className="flex items-center space-x-2 text-purple-500 dark:text-purple-400">
                  <Clock className="h-4 w-4" />
                  <span className="text-sm">Always evolving</span>
                </div>
              </CardFooter>
            </Card>
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-blue-950/30 py-16">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-5xl">
            <div className="mb-12 text-center">
              <div className="mb-4 mx-auto inline-flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-primary/20 to-blue-500/20 ring-1 ring-primary/20">
                <Award className="h-6 w-6 text-primary animate-pulse" />
              </div>
              <h2 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-primary">Our Core Values</h2>
              <p className="mt-4 mx-auto max-w-2xl text-muted-foreground">
                These principles guide everything we do at DuvaInsights, from the content we create to how we interact with our community.
              </p>
            </div>

            <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
              {values.map((value) => {
                const Icon = value.icon
                return (
                  <Card key={value.title} className="rounded-lg border bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm text-card-foreground shadow-sm transition-all duration-200 hover:shadow-md focus-within:ring-1 focus-within:ring-primary/40 overflow-hidden group hover:-translate-y-1">
                    <CardHeader className="p-5">
                      <div className={`mb-4 flex h-12 w-12 items-center justify-center rounded-full ${value.bgColor} ring-1 ring-primary/10 group-hover:shadow-md transition-all duration-300`}>
                        <Icon className={`h-6 w-6 ${value.color} group-hover:scale-110 transition-transform`} />
                      </div>
                      <h3 className="text-xl font-semibold group-hover:text-primary transition-colors">{value.title}</h3>
                    </CardHeader>
                    <CardContent className="p-5 pt-0">
                      <p className="text-muted-foreground">{value.description}</p>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="container mx-auto px-4 py-20">
        <Card className="rounded-lg border bg-white dark:bg-gradient-to-br dark:from-slate-900 dark:to-blue-950/50 text-card-foreground shadow-md transition-all duration-200 hover:shadow-lg focus-within:ring-1 focus-within:ring-primary/40 mx-auto max-w-4xl overflow-hidden">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-background/90 z-10"></div>
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-primary/30 to-transparent rounded-full -mr-20 -mt-20 opacity-70"></div>
            <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-blue-500/30 to-transparent rounded-full -ml-20 -mb-20 opacity-70"></div>

            <div className="relative z-20 p-8 md:p-12 flex flex-col md:flex-row items-center gap-8">
              <div className="flex-1 text-center md:text-left">
                <div className="inline-flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-primary/20 to-blue-500/20 mb-6 ring-1 ring-primary/20">
                  <TrendingUp className="h-6 w-6 text-primary animate-pulse" />
                </div>
                <h2 className="text-3xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-600 dark:from-primary dark:to-blue-400">Join Our Community</h2>
                <p className="text-muted-foreground mb-8 max-w-xl">
                  Be part of a growing network of forward-thinking professionals who are shaping the future by understanding emerging trends first.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 sm:justify-start justify-center">
                  <Button asChild size="lg" className="gap-2 bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-600/90">
                    <Link href="/newsletter">
                      Subscribe to Newsletter
                    </Link>
                  </Button>
                  <Button asChild variant="outline" size="lg" className="border-primary/20 hover:bg-primary/5">
                    <Link href="/contact">
                      Get in Touch
                    </Link>
                  </Button>
                </div>
              </div>

              <div className="hidden md:block">
                <div className="relative w-64 h-64 rounded-full bg-gradient-to-tr from-primary/10 to-transparent flex items-center justify-center ring-1 ring-primary/10">
                  <div className="absolute inset-0 border-2 border-dashed border-primary/30 rounded-full animate-spin-slow"></div>
                  <div className="w-48 h-48 rounded-full bg-gradient-to-br from-background to-muted flex items-center justify-center ring-1 ring-primary/10">
                    <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-primary/20 to-blue-500/20 shadow-inner ring-1 ring-primary/20">
                      <TrendingUp className="h-8 w-8 text-primary animate-pulse" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </section>

      {/* Add animation style for spinning effect */}
      <style jsx global>{`
        @keyframes spin-slow {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }
        .animate-spin-slow {
          animation: spin-slow 20s linear infinite;
        }
      `}</style>
    </div>
  )
}
