<svg
  width="50"
  height="50"
  viewBox="0 0 50 50"
  fill="none"
  xmlns="http://www.w3.org/2000/svg"
>
  <!-- Gradients Definition -->
  <defs>
    <linearGradient id="outerGradient" x1="5" y1="5" x2="45" y2="45" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#3459D1" />
      <stop offset="50%" stop-color="#604DE2" />
      <stop offset="100%" stop-color="#873AE1" />
    </linearGradient>
    
    <linearGradient id="innerGradient" x1="10" y1="10" x2="40" y2="40" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#3459D1" />
      <stop offset="100%" stop-color="#873AE1" />
    </linearGradient>
  </defs>
  
  <!-- Outer Circle - Light Ring -->
  <circle cx="25" cy="25" r="23" stroke="url(#outerGradient)" stroke-width="2" />
  
  <!-- Inner Circle - Filled -->
  <circle cx="25" cy="25" r="18" fill="url(#innerGradient)" />
  
  <!-- Trend Lines -->
  <path
    d="M15 30L22 23L28 27L35 18"
    stroke="white"
    stroke-width="2.5"
    stroke-linecap="round"
    stroke-linejoin="round"
  />
  
  <!-- Insight Dot - Pulse Animation -->
  <circle cx="35" cy="18" r="3" fill="white" class="animate-pulse" />
</svg> 