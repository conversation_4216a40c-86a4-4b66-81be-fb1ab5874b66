<!DOCTYPE html>
<html lang="en">
<head>
  <title>DuvaInsights Logo for Export</title>
  <style>
    body { 
      margin: 0; 
      padding: 0; 
      background-color: transparent;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
    }
    .container {
      width: 512px;
      height: 512px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    svg {
      width: 90%;
      height: 90%;
    }
    /* Logo pulse animation */
    .animate-pulse {
      animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    @keyframes pulse {
      0%, 100% {
        opacity: 1;
        transform: scale(1);
      }
      50% {
        opacity: 0.7;
        transform: scale(1.1);
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <svg
      viewBox="0 0 50 50"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <!-- Gradients Definition -->
      <defs>
        <linearGradient id="outerGradient" x1="5" y1="5" x2="45" y2="45" gradientUnits="userSpaceOnUse">
          <stop offset="0%" stop-color="#3459D1" />
          <stop offset="50%" stop-color="#604DE2" />
          <stop offset="100%" stop-color="#873AE1" />
        </linearGradient>
        
        <linearGradient id="innerGradient" x1="10" y1="10" x2="40" y2="40" gradientUnits="userSpaceOnUse">
          <stop offset="0%" stop-color="#3459D1" />
          <stop offset="100%" stop-color="#873AE1" />
        </linearGradient>
      </defs>
      
      <!-- Outer Circle - Light Ring -->
      <circle cx="25" cy="25" r="23" stroke="url(#outerGradient)" stroke-width="2" />
      
      <!-- Inner Circle - Filled -->
      <circle cx="25" cy="25" r="18" fill="url(#innerGradient)" />
      
      <!-- Trend Lines -->
      <path
        d="M15 30L22 23L28 27L35 18"
        stroke="white"
        stroke-width="2.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      
      <!-- Insight Dot - Pulse Animation -->
      <circle cx="35" cy="18" r="3" fill="white" class="animate-pulse" />
    </svg>
  </div>
  <script>
    // This file can be opened in a browser and then screenshotted or 
    // saved as an image using browser tools or screenshot utilities
    console.log('Open this page and take a screenshot at the desired size');
  </script>
</body>
</html> 