# DuvaInsight - Modern Trend Discovery Platform

DuvaInsight is a production-ready blog-style web application built with Next.js 15+ and React 19+ for discovering and learning about emerging trends across technology, business, health, culture, finance, and creative industries.

![DuvaInsight Preview](public/preview.png)

## 🚀 Features

### Content & Discovery
- **Blog-style articles** with MDX support for rich content
- **Category-based navigation** for easy exploration
- **Trending topics** tracking with growth metrics
- **Full-text search** with real-time results
- **Archive page** with filtering and pagination
- **Related articles** suggestions

### User Experience
- **Dark/Light mode** with system preference detection
- **Responsive design** optimized for all devices
- **Fast page loads** with static site generation
- **Newsletter subscription** integration
- **Social sharing** capabilities
- **Reading time estimates** for all articles
- **Table of contents** for long-form content

### Technical Features
- **SEO optimized** with meta tags and structured data
- **RSS feed** generation
- **Sitemap** for search engines
- **Error boundaries** for graceful error handling
- **Loading states** for better UX
- **TypeScript** for type safety
- **Tailwind CSS** for styling

## 🛠️ Tech Stack

- **Framework**: Next.js 15+
- **UI Library**: React 19+
- **Styling**: Tailwind CSS
- **Content**: MDX for articles
- **Icons**: Lucide React
- **Animation**: Framer Motion
- **Search**: Fuse.js
- **Theme**: next-themes
- **Date Formatting**: date-fns
- **Code Quality**: TypeScript, ESLint

## 📦 Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/duvainsight.git
cd duvainsight
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Copy environment variables:
```bash
cp .env.example .env.local
```

4. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📁 Project Structure

```
duvainsight/
├── app/                    # Next.js app directory
│   ├── (routes)/          # Page routes
│   ├── layout.tsx         # Root layout
│   ├── page.tsx           # Homepage
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── article-card.tsx   # Article preview card
│   ├── header.tsx         # Site header
│   ├── footer.tsx         # Site footer
│   └── ...                # Other components
├── content/               # MDX content files
│   ├── technology/        # Tech articles
│   ├── business/          # Business articles
│   └── ...                # Other categories
├── lib/                   # Utility functions
├── public/                # Static assets
└── package.json           # Dependencies
```

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Import project to Vercel
3. Configure environment variables
4. Deploy

### Other Platforms
The app can be deployed to any platform that supports Next.js:
- Netlify
- AWS Amplify
- Google Cloud Run
- Docker containers

## 📝 Content Management

### Adding Articles
1. Create a new `.mdx` file in the appropriate category folder under `content/`
2. Add frontmatter with required fields:
```yaml
---
title: "Your Article Title"
excerpt: "Brief description"
author: "Author Name"
date: "2024-01-20"
category: "technology"
tags: ["tag1", "tag2"]
readingTime: "5 min"
---
```
3. Write your content using Markdown and MDX components

### Categories
- Technology
- Business
- Health
- Culture
- Finance
- Creative

## 🔧 Configuration

### Site Metadata
Edit `app/layout.tsx` to update site metadata:
- Title
- Description
- Social media links
- Open Graph images

### Theme Customization
Modify `tailwind.config.js` to customize:
- Colors
- Typography
- Spacing
- Animations

## 📊 Analytics

The app is prepared for analytics integration:
- Google Analytics
- Google Tag Manager
- Custom analytics solutions

Add your tracking IDs to environment variables.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Next.js team for the amazing framework
- Vercel for hosting and deployment
- Open source community for inspiration

## 📞 Support

For support, email <EMAIL> or join our community Discord.

---

Built with ❤️ for the future-curious
