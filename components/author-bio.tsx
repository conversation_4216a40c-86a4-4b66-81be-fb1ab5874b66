import { Twitter, Linkedin, Globe } from 'lucide-react'

// <PERSON>ck author data - in production, this would come from a database
const authors: Record<string, {
  name: string
  bio: string
  avatar: string
  twitter?: string
  linkedin?: string
  website?: string
}> = {
  'Dr. <PERSON>': {
    name: 'Dr. <PERSON>',
    bio: 'AI researcher and tech futurist with 15 years of experience in machine learning. Former research scientist at DeepMind, now leading AI ethics initiatives.',
    avatar: '/avatars/sarah-chen.jpg',
    twitter: 'sarahchen_ai',
    linkedin: 'sarah-chen-ai',
    website: 'sarahchen.ai',
  },
  '<PERSON>': {
    name: '<PERSON>',
    bio: 'Business strategist and remote work advocate. Helping companies build distributed teams and digital-first cultures for over a decade.',
    avatar: '/avatars/mark-thompson.jpg',
    twitter: 'markthompson',
    linkedin: 'mark-thompson-biz',
  },
  'Dr. <PERSON>': {
    name: 'Dr. <PERSON>',
    bio: 'Longevity researcher and biotech entrepreneur. Passionate about making cutting-edge health science accessible to everyone.',
    avatar: '/avatars/emily-rodriguez.jpg',
    twitter: 'emilyrod_health',
    website: 'longevitylab.com',
  },
  '<PERSON>': {
    name: '<PERSON> <PERSON>',
    bio: 'Digital culture analyst and creator economy expert. Tracking the evolution of online communities and creative entrepreneurship.',
    avatar: '/avatars/alex-kim.jpg',
    twitter: 'alexkim_digital',
    linkedin: 'alex-kim-creator',
  },
}

export function AuthorBio({ author }: { author: string }) {
  const authorData = authors[author]

  if (!authorData) {
    return null
  }

  return (
    <div className="flex gap-4">
      <div className="h-16 w-16 rounded-full bg-gradient-to-br from-primary/20 to-primary/5 flex-shrink-0" />
      <div className="flex-1">
        <h3 className="mb-2 text-lg font-semibold">{authorData.name}</h3>
        <p className="mb-3 text-muted-foreground">{authorData.bio}</p>
        <div className="flex gap-3">
          {authorData.twitter && (
            <a
              href={`https://twitter.com/${authorData.twitter}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-muted-foreground hover:text-primary transition-colors"
              aria-label="Twitter"
            >
              <Twitter className="h-5 w-5" />
            </a>
          )}
          {authorData.linkedin && (
            <a
              href={`https://linkedin.com/in/${authorData.linkedin}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-muted-foreground hover:text-primary transition-colors"
              aria-label="LinkedIn"
            >
              <Linkedin className="h-5 w-5" />
            </a>
          )}
          {authorData.website && (
            <a
              href={`https://${authorData.website}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-muted-foreground hover:text-primary transition-colors"
              aria-label="Website"
            >
              <Globe className="h-5 w-5" />
            </a>
          )}
        </div>
      </div>
    </div>
  )
}
