'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/shared/utils';
import { mainCategories } from '@/lib/categoryMappings';

interface BackButtonProps {
  className?: string;
  fallbackUrl?: string;
  fallbackLabel?: string;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
}



export function BackButton({
  className,
  fallbackUrl = '/',
  fallbackLabel = 'Back',
  variant = 'ghost',
  size = 'sm',
}: BackButtonProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [backUrl, setBackUrl] = useState<string>(fallbackUrl);
  const [label, setLabel] = useState<string>(fallbackLabel);

  useEffect(() => {
    // Determine hierarchical back navigation based on current path
    const segments = pathname.split('/').filter(Boolean);

    if (segments.length === 0) {
      // Already at homepage
      setBackUrl('/');
      setLabel('Home');
      return;
    }

    // Handle post pages: /post/[category]/[slug]
    if (segments[0] === 'post' && segments.length >= 3) {
      const categorySlug = segments[1];
      const categoryName = Object.entries(mainCategories).find(
        ([_, slug]) => slug === categorySlug
      )?.[0];

      setBackUrl(`/category/${categorySlug}`);
      setLabel(`Back to ${categoryName || categorySlug.charAt(0).toUpperCase() + categorySlug.slice(1)}`);
      return;
    }

    // Handle category pages: /category/[slug]
    if (segments[0] === 'category' && segments.length >= 2) {
      setBackUrl('/');
      setLabel('Back to Homepage');
      return;
    }

    // Handle subcategory pages: /subcategory/[slug]
    if (segments[0] === 'subcategory' && segments.length >= 2) {
      // For now, subcategory pages go back to homepage
      // TODO: Implement proper subcategory to main category mapping
      setBackUrl('/');
      setLabel('Back to Homepage');
      return;
    }

    // Handle other pages (trending, archive, about, etc.)
    if (segments.length === 1) {
      setBackUrl('/');
      setLabel('Back to Homepage');
      return;
    }

    // Default fallback
    setBackUrl(fallbackUrl);
    setLabel(fallbackLabel);
  }, [pathname, fallbackUrl, fallbackLabel]);

  const handleClick = () => {
    router.push(backUrl);
  };

  return (
    <Button
      variant={variant}
      size={size}
      className={cn("gap-1 text-muted-foreground hover:text-foreground", className)}
      onClick={handleClick}
    >
      <ArrowLeft className="h-4 w-4 flex-shrink-0" />
      <span className="truncate">{label}</span>
    </Button>
  );
}
