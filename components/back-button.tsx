'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/shared/utils';
import { mainCategories } from '@/lib/categoryMappings';

interface BackButtonProps {
  className?: string;
  fallbackUrl?: string;
  fallbackLabel?: string;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
}

// Store navigation history in memory
const HISTORY_KEY = 'navigationHistory';

// Helper function to get page title from path
const getPageTitleFromPath = (path: string): string => {
  if (!path) return 'Home';

  const segments = path.split('/').filter(Boolean);

  if (segments.length === 0) return 'Home';

  // Handle special routes
  if (segments[0] === 'trending') return 'Trending';
  if (segments[0] === 'archive') return 'Archive';

  // Handle category pages
  if (segments[0] === 'category' && segments.length > 1) {
    // Find the category name from the slug
    const categorySlug = segments[1];
    const categoryName = Object.entries(mainCategories).find(
      ([_, slug]) => slug === categorySlug
    )?.[0];

    return categoryName ? categoryName : categorySlug.charAt(0).toUpperCase() + categorySlug.slice(1);
  }

  // Handle subcategory pages
  if (segments[0] === 'subcategory' && segments.length > 1) {
    const subcategorySlug = segments[1];
    return subcategorySlug
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  // Handle post pages
  if (segments[0] === 'post' && segments.length > 2) {
    if (segments[1] === 'category') {
      return segments[2].charAt(0).toUpperCase() + segments[2].slice(1);
    }
    return 'Post';
  }

  // Default case
  const lastSegment = segments[segments.length - 1];
  return lastSegment.charAt(0).toUpperCase() + lastSegment.slice(1);
};

export function BackButton({
  className,
  fallbackUrl = '/',
  fallbackLabel = 'Back',
  variant = 'ghost',
  size = 'sm',
}: BackButtonProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [previousPath, setPreviousPath] = useState<string | null>(null);
  const [label, setLabel] = useState<string>(fallbackLabel);

  useEffect(() => {
    // Function to update navigation history
    const updateNavigationHistory = () => {
      try {
        // Get existing history
        const historyJson = sessionStorage.getItem(HISTORY_KEY);
        let history: string[] = historyJson ? JSON.parse(historyJson) : [];

        // Don't add duplicates of the current path
        if (history.length > 0 && history[history.length - 1] === pathname) {
          return;
        }

        // Add current path to history (limited to last 10 paths)
        history.push(pathname);
        if (history.length > 10) {
          history = history.slice(-10);
        }

        // Save updated history
        sessionStorage.setItem(HISTORY_KEY, JSON.stringify(history));
      } catch (error) {
        console.error('Error updating navigation history:', error);
      }
    };

    // Function to determine previous path and set label
    const determinePreviousPath = () => {
      try {
        const historyJson = sessionStorage.getItem(HISTORY_KEY);
        if (!historyJson) return;

        const history: string[] = JSON.parse(historyJson);

        // Find the previous path (excluding the current path)
        const currentIndex = history.lastIndexOf(pathname);
        let prevPath = null;

        if (currentIndex > 0) {
          // Get the path that led to the current path
          prevPath = history[currentIndex - 1];
        } else if (history.length > 1) {
          // If we can't find the exact previous path, use the second-to-last path
          prevPath = history[history.length - 2];
        }

        if (prevPath && prevPath !== pathname) {
          setPreviousPath(prevPath);
          const pageTitle = getPageTitleFromPath(prevPath);
          setLabel(`Back to ${pageTitle}`);
        } else {
          setPreviousPath(fallbackUrl);
          setLabel(fallbackLabel);
        }
      } catch (error) {
        console.error('Error determining previous path:', error);
        setPreviousPath(fallbackUrl);
        setLabel(fallbackLabel);
      }
    };

    // Update history and determine previous path
    updateNavigationHistory();
    determinePreviousPath();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname]);

  const handleClick = () => {
    if (previousPath) {
      router.push(previousPath);
    } else if (fallbackUrl) {
      router.push(fallbackUrl);
    } else {
      router.back();
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      className={cn("gap-1 text-muted-foreground hover:text-foreground", className)}
      onClick={handleClick}
    >
      <ArrowLeft className="h-4 w-4 flex-shrink-0" />
      <span className="truncate">{label}</span>
    </Button>
  );
}
