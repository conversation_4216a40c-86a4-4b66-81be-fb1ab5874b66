import Link from 'next/link'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card'
import { Calendar, Clock } from 'lucide-react'
import { CategoryBadge } from '@/components/category-badge'

interface Article {
  id: string
  title: string
  excerpt: string
  category: string
  subCategory: string
  slug: string
  author: string
  date: string
  readingTime: string
}

export function ArticleCard({ article }: { article: Article }) {
  // Get the main category slug from the subcategory
  const mainCategorySlug = article.category;
  const subCategorySlug = article.subCategory;

  return (
    <Card className="overflow-hidden transition-all hover:shadow-md flex flex-col h-full">
      <Link href={`/post/${mainCategorySlug}/${article.slug}`} className="flex flex-col h-full">
        <CardHeader className="p-4">
          <CategoryBadge
            category={mainCategorySlug}
            subCategory={subCategorySlug}
            className="mb-2 w-fit"
            showSparkle={true}
          >
            {article.subCategory ? article.subCategory.replace(/-/g, ' ') : article.category}
          </CategoryBadge>
          <h3 className="line-clamp-2 text-xl font-bold min-h-[3.5rem] leading-tight">{article.title}</h3>
        </CardHeader>
        <CardContent className="p-4 pt-0 flex-grow">
          <p className="line-clamp-3 text-muted-foreground min-h-[4.5rem] leading-normal">{article.excerpt}</p>
        </CardContent>
        <CardFooter className="p-4 mt-auto border-t border-border/50">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground text-sm align-middle leading-normal">
                {new Date(article.date).toLocaleDateString()}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground text-sm align-middle leading-normal">
                {article.readingTime}
              </span>
            </div>
          </div>
        </CardFooter>
      </Link>
    </Card>
  )
}
