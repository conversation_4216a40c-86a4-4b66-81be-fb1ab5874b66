import Link from 'next/link'
import Image from 'next/image'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader } from '@/components/ui/card'
import { Clock, Eye } from 'lucide-react'
import { CategoryBadge } from '@/components/category-badge'

interface Article {
  id: string
  title: string
  excerpt: string
  category: string
  subCategory: string
  slug: string
  author: string
  date: string
  readingTime: string
  imageUrl?: string
}

export function ArticleCard({ article }: { article: Article }) {
  // Get the main category slug from the subcategory
  const mainCategorySlug = article.category;
  const subCategorySlug = article.subCategory;

  // Generate mock view count for display
  const viewCount = Math.floor(Math.random() * 50) + 10;
  const viewGrowth = Math.floor(Math.random() * 200) + 50;

  return (
    <Card className="overflow-hidden transition-all hover:shadow-md flex flex-col h-full bg-card">
      <Link href={`/post/${mainCategorySlug}/${article.slug}`} className="flex flex-col h-full">
        {/* Image */}
        <div className="relative aspect-video w-full overflow-hidden">
          {article.imageUrl ? (
            <Image
              src={article.imageUrl}
              alt={article.title}
              fill
              className="object-cover transition-transform hover:scale-105"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-muted to-muted/50 flex items-center justify-center">
              <div className="text-muted-foreground text-sm">No image available</div>
            </div>
          )}
        </div>

        {/* Content */}
        <CardHeader className="p-4 pb-2">
          <CategoryBadge
            category={mainCategorySlug}
            subCategory={subCategorySlug}
            className="mb-2 w-fit"
            showSparkle={true}
          >
            {article.subCategory ? article.subCategory.replace(/-/g, ' ') : article.category}
          </CategoryBadge>
          <h3 className="line-clamp-2 text-lg font-bold min-h-[3rem] leading-tight">{article.title}</h3>
        </CardHeader>

        <CardContent className="p-4 pt-0 flex-grow">
          <p className="line-clamp-2 text-muted-foreground text-sm leading-relaxed">{article.excerpt}</p>
        </CardContent>

        <CardFooter className="p-4 pt-0 mt-auto">
          <div className="flex items-center justify-between w-full text-xs text-muted-foreground">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                <Eye className="h-3 w-3" />
                <span>{viewCount.toLocaleString()} views</span>
              </div>
              <div className="flex items-center space-x-1">
                <span className="text-green-500">+{viewGrowth}%</span>
              </div>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="h-3 w-3" />
              <span>{article.readingTime}</span>
            </div>
          </div>
        </CardFooter>
      </Link>
    </Card>
  )
}
