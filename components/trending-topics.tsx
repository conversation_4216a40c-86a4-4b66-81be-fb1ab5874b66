import Link from 'next/link'
import { TrendingUp, Zap } from 'lucide-react'

const trendingTopics = [
  {
    id: '1',
    title: 'Quantum Computing',
    posts: 24,
    growth: '+85%',
    hot: true,
  },
  {
    id: '2',
    title: 'Sustainable Tech',
    posts: 31,
    growth: '+62%',
    hot: true,
  },
  {
    id: '3',
    title: 'Web3 & Metaverse',
    posts: 19,
    growth: '+45%',
    hot: false,
  },
  {
    id: '4',
    title: 'Mental Health Tech',
    posts: 27,
    growth: '+38%',
    hot: false,
  },
  {
    id: '5',
    title: 'Space Economy',
    posts: 15,
    growth: '+92%',
    hot: true,
  },
]

export function TrendingTopics() {
  return (
    <div className="rounded-lg border bg-card p-6">
      <div className="mb-4 flex items-center gap-2">
        <TrendingUp className="h-5 w-5 text-primary" />
        <h3 className="text-lg font-semibold">Trending Topics</h3>
      </div>
      
      <div className="space-y-3">
        {trendingTopics.map((topic, index) => (
          <Link
            key={topic.id}
            href={`/topic/${topic.title.toLowerCase().replace(/\s+/g, '-')}`}
            className="group flex items-center justify-between rounded-md p-3 hover:bg-muted transition-colors"
          >
            <div className="flex items-center gap-3">
              <span className="text-2xl font-bold text-muted-foreground">
                {String(index + 1).padStart(2, '0')}
              </span>
              <div>
                <div className="flex items-center gap-2">
                  <span className="font-medium group-hover:text-primary transition-colors">
                    {topic.title}
                  </span>
                  {topic.hot && (
                    <TrendingUp className="h-4 w-4 text-orange-500" />
                  )}
                </div>
                <span className="text-sm text-muted-foreground">
                  {topic.posts} articles
                </span>
              </div>
            </div>
            <div className="flex items-center gap-1 text-sm font-medium text-green-600 dark:text-green-400">
              <Zap className="h-3 w-3" />
              {topic.growth}
            </div>
          </Link>
        ))}
      </div>
      
      <Link
        href="/trending"
        className="mt-4 block text-center text-sm font-medium text-muted-foreground hover:text-primary transition-colors"
      >
        View all trending topics →
      </Link>
    </div>
  )
}
