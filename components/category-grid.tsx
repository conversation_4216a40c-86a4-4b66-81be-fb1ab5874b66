'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link'
import {
  Cpu,
  Briefcase,
  Heart,
  Users,
  DollarSign,
  Palette,
  ArrowRight
} from 'lucide-react'
import SubcategoryLink from './subcategory-link'

// Helper function to extract color name from category color
function getColorName(categoryColor: string) {
  return categoryColor.split('-')[1] || 'gray';
}

const categories = [
  {
    name: 'Technology',
    slug: 'technology',
    icon: Cpu,
    color: 'text-blue-600 dark:text-blue-400',
    bgColor: 'bg-blue-100 dark:bg-blue-900/20',
    description: 'AI, software, hardware, and digital innovation',
    postCount: 156,
  },
  {
    name: 'Business',
    slug: 'business',
    icon: Briefcase,
    color: 'text-purple-600 dark:text-purple-400',
    bgColor: 'bg-purple-100 dark:bg-purple-900/20',
    description: 'Leadership, strategy, and corporate trends',
    postCount: 143,
  },
  {
    name: 'Health',
    slug: 'health',
    icon: Heart,
    color: 'text-red-600 dark:text-red-400',
    bgColor: 'bg-red-100 dark:bg-red-900/20',
    description: 'Wellness, medicine, and biotechnology',
    postCount: 98,
  },
  {
    name: 'Culture',
    slug: 'culture',
    icon: Users,
    color: 'text-green-600 dark:text-green-400',
    bgColor: 'bg-green-100 dark:bg-green-900/20',
    description: 'Society, lifestyle, and cultural shifts',
    postCount: 112,
  },
  {
    name: 'Finance',
    slug: 'finance',
    icon: DollarSign,
    color: 'text-yellow-600 dark:text-yellow-400',
    bgColor: 'bg-yellow-100 dark:bg-yellow-900/20',
    description: 'Markets, crypto, and economic trends',
    postCount: 87,
  },
  {
    name: 'Creative',
    slug: 'creative',
    icon: Palette,
    color: 'text-pink-600 dark:text-pink-400',
    bgColor: 'bg-pink-100 dark:bg-pink-900/20',
    description: 'Design, art, and creative industries',
    postCount: 76,
  },
]

// Define subcategory type
interface Subcategory {
  name: string;
  slug: string;
}

// Define subcategories map outside the component to avoid re-computation
const subcategoriesMap: Record<string, Subcategory[]> = {
  'Technology': [
    { name: 'AI & Machine Learning', slug: 'ai-machine-learning' },
    { name: 'Web & Mobile Development', slug: 'web-mobile-development' },
    { name: 'Cybersecurity & Privacy', slug: 'cybersecurity-privacy' },
    { name: 'DevOps & Cloud', slug: 'devops-cloud' },
    { name: 'Hardware & IoT', slug: 'hardware-iot' },
  ],
  'Business': [
    { name: 'Strategy & Planning', slug: 'strategy-planning' },
    { name: 'Leadership & Management', slug: 'leadership-management' },
    { name: 'Marketing & Sales', slug: 'marketing-sales' },
    { name: 'Operations', slug: 'operations' },
    { name: 'Entrepreneurship', slug: 'entrepreneurship' },
  ],
  'Health': [
    { name: 'Medical Research', slug: 'medical-research' },
    { name: 'Wellness & Fitness', slug: 'wellness-fitness' },
    { name: 'Mental Health', slug: 'mental-health' },
    { name: 'Healthcare Systems', slug: 'healthcare-systems' },
    { name: 'Nutrition', slug: 'nutrition' },
  ],
  'Culture': [
    { name: 'Arts & Entertainment', slug: 'arts-entertainment' },
    { name: 'Social Trends', slug: 'social-trends' },
    { name: 'Education', slug: 'education' },
    { name: 'Media', slug: 'media' },
    { name: 'Lifestyle', slug: 'lifestyle' },
  ],
  'Finance': [
    { name: 'Investing', slug: 'investing' },
    { name: 'Cryptocurrency', slug: 'cryptocurrency' },
    { name: 'Personal Finance', slug: 'personal-finance' },
    { name: 'Banking', slug: 'banking' },
    { name: 'Economics', slug: 'economics' },
  ],
  'Creative': [
    { name: 'Design', slug: 'design' },
    { name: 'Writing', slug: 'writing' },
    { name: 'Visual Arts', slug: 'visual-arts' },
    { name: 'Music & Audio', slug: 'music-audio' },
    { name: 'Film & Video', slug: 'film-video' },
  ],
};

export function CategoryGrid() {
  // Use useEffect to ensure client-side only rendering
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // If not client yet, render a simplified version to avoid hydration errors
  if (!isClient) {
    return (
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {categories.map((category) => (
          <div key={category.slug} className="rounded-lg border bg-card p-6">
            <div className="h-6 w-6 mb-4"></div>
            <h3 className="mb-2 text-lg font-semibold">{category.name}</h3>
            <p className="mb-4 text-sm text-muted-foreground">{category.description}</p>
          </div>
        ))}
      </div>
    );
  }

  // Full client-side rendering
  return (
    <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
      {categories.map((category) => {
        const Icon = category.icon;
        // Get subcategories from the pre-defined map
        const subcategories = subcategoriesMap[category.name] || [];

        return (
          <Link
            key={category.slug}
            href={`/category/${category.slug}`}
            className="group relative overflow-hidden rounded-lg border bg-card p-6 hover:shadow-md transition-all duration-300"
          >
            <div className={`mb-4 inline-flex rounded-lg p-3 ${category.bgColor}`}>
              <Icon className={`h-6 w-6 ${category.color}`} />
            </div>
            <h3 className="mb-2 text-lg font-semibold group-hover:text-primary transition-colors">
              {category.name}
            </h3>
            <p className="mb-4 text-sm text-muted-foreground">
              {category.description}
            </p>

            {/* Subcategories - Always render the div to avoid hydration errors */}
            <div className="mb-4">
              <div className="flex flex-wrap gap-2">
                {subcategories.slice(0, 3).map((subcat: Subcategory) => (
                  <SubcategoryLink
                    key={subcat.slug}
                    href={`/subcategory/${subcat.slug}`}
                    color={getColorName(category.color)}
                  >
                    {subcat.name}
                  </SubcategoryLink>
                ))}
                {subcategories.length > 3 && (
                  <SubcategoryLink
                    href={`/category/${category.slug}`}
                    color={getColorName(category.color)}
                  >
                    +{subcategories.length - 3} more
                  </SubcategoryLink>
                )}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">
                {category.postCount} articles
              </span>
              <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
            </div>
          </Link>
        )
      })}
    </div>
  )
}
