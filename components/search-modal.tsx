'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { Search, X, TrendingUp, Clock, ArrowRight } from 'lucide-react'
import Fuse from 'fuse.js'
import { motion, AnimatePresence } from 'framer-motion'
import { getAllDocuments } from '@/lib/supabaseUtils'

interface SearchResult {
  id: string
  title: string
  excerpt: string
  category: string
  subCategory: string
  slug: string
  date: string
}

// We'll fetch real data from our API
const recentSearches = ['AI trends', 'web development', 'cybersecurity', 'machine learning']

export function SearchModal({ open, onClose }: { open: boolean; onClose: () => void }) {
  const router = useRouter()
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [posts, setPosts] = useState<SearchResult[]>([])
  const [trendingPosts, setTrendingPosts] = useState<SearchResult[]>([])

  // Fetch documents when the modal opens
  useEffect(() => {
    if (open) {
      const fetchDocuments = async () => {
        setIsSearching(true)
        try {
          // Add a console log to see what's happening
          console.log('Fetching documents for search...')

          const allDocuments = await getAllDocuments()
          console.log('Documents received:', allDocuments)

          if (!allDocuments || allDocuments.length === 0) {
            console.log('No documents returned from API')
            setPosts([])
            setTrendingPosts([])
            setIsSearching(false)
            return
          }

          // Transform documents to SearchResult format
          const formattedPosts: SearchResult[] = allDocuments.map((doc: any) => ({
            id: doc.id || 'unknown-id',
            title: doc.title || 'Untitled Document',
            excerpt: doc.metadata?.metadata?.primary_objective || 'No description available',
            category: doc.mainCategorySlug || 'uncategorized',
            subCategory: doc.subCategory || '',
            slug: doc.id || 'unknown-id',
            date: doc.createdAt ? new Date(doc.createdAt).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]
          }))

          console.log('Formatted posts:', formattedPosts)
          setPosts(formattedPosts)

          // Set trending posts (most recent 3)
          setTrendingPosts(formattedPosts.slice(0, 3))
          console.log('Trending posts set:', formattedPosts.slice(0, 3))

          setIsSearching(false)
        } catch (error) {
          console.error('Error fetching documents:', error)
          setPosts([])
          setTrendingPosts([])
          setIsSearching(false)
        }
      }

      fetchDocuments()
    }
  }, [open])

  // Initialize Fuse.js for fuzzy search, memoized with useMemo
  const fuse = useMemo(() => {
    console.log('Creating Fuse instance with posts:', posts);
    return new Fuse(posts.length > 0 ? posts : [], {
      keys: ['title', 'excerpt', 'category', 'subCategory'],
      threshold: 0.3,
    });
  }, [posts]);

  const handleSearch = useCallback(
    (searchQuery: string) => {
      if (searchQuery.trim() === '') {
        setResults([])
        return
      }

      setIsSearching(true)
      console.log('Searching for:', searchQuery)
      console.log('Posts available for search:', posts.length)

      // Small delay for better UX
      setTimeout(() => {
        try {
          if (posts.length === 0) {
            console.log('No posts available for search')
            setResults([])
            setIsSearching(false)
            return
          }

          // Use Fuse.js for fuzzy search
          const searchResults = fuse.search(searchQuery)
          console.log('Search results:', searchResults)
          setResults(searchResults.map((result) => result.item))
        } catch (error) {
          console.error('Search error:', error);
          // Simple search fallback if Fuse.js fails
          console.log('Falling back to simple search')
          const simpleResults = posts.filter((post: SearchResult) =>
            post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            post.excerpt?.toLowerCase().includes(searchQuery.toLowerCase())
          );
          console.log('Simple search results:', simpleResults)
          setResults(simpleResults);
        }
        setIsSearching(false)
      }, 300)
    },
    [fuse, posts]
  )

  useEffect(() => {
    if (query) {
      handleSearch(query)
    } else {
      setResults([])
    }
  }, [query, handleSearch])

  const handleResultClick = (slug: string, category: string) => {
    // Navigate to the document page
    router.push(`/post/${category}/${slug}`)
    onClose()
    setQuery('')

    // Save to recent searches in localStorage (if we wanted to implement this)
    // const searches = JSON.parse(localStorage.getItem('recentSearches') || '[]')
    // if (!searches.includes(query) && query.trim() !== '') {
    //   searches.unshift(query)
    //   localStorage.setItem('recentSearches', JSON.stringify(searches.slice(0, 5)))
    // }
  }

  const handleRecentSearch = (search: string) => {
    setQuery(search)
  }

  if (!open) return null

  return (
    <AnimatePresence>
      {open && (
        <>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm"
            onClick={onClose}
          />
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="fixed left-[50%] top-[50%] z-50 w-full max-w-2xl translate-x-[-50%] translate-y-[-50%] p-4"
          >
            <div className="rounded-lg border bg-background shadow-lg">
              <div className="flex items-center border-b px-4">
                <Search className="h-5 w-5 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="Search articles, trends, insights..."
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  className="flex-1 px-4 py-4 text-base outline-none placeholder:text-muted-foreground"
                  autoFocus
                />
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-muted rounded-md transition-colors"
                  aria-label="Close search"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <div className="max-h-[400px] overflow-y-auto">
                {query === '' ? (
                  <div className="p-4">
                    <div className="mb-4">
                      <h3 className="mb-2 text-sm font-semibold text-muted-foreground">Recent Searches</h3>
                      <div className="flex flex-wrap gap-2">
                        {recentSearches.map((search) => (
                          <button
                            key={search}
                            onClick={() => handleRecentSearch(search)}
                            className="flex items-center gap-1 rounded-full bg-muted px-3 py-1 text-sm hover:bg-muted/80 transition-colors"
                          >
                            <Clock className="h-3 w-3" />
                            {search}
                          </button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h3 className="mb-2 text-sm font-semibold text-muted-foreground">Trending Topics</h3>
                      <div className="space-y-2">
                        {isSearching ? (
                          <div className="flex items-center justify-center py-4">
                            <div className="h-5 w-5 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                          </div>
                        ) : trendingPosts.length > 0 ? (
                          trendingPosts.map((post) => (
                            <button
                              key={post.id}
                              onClick={() => handleResultClick(post.slug, post.category)}
                              className="flex w-full items-center gap-3 rounded-md p-2 text-left hover:bg-muted transition-colors"
                            >
                              <TrendingUp className="h-4 w-4 text-primary" />
                              <span className="text-sm">{post.title}</span>
                            </button>
                          ))
                        ) : (
                          <div className="py-2 text-sm text-muted-foreground">
                            No trending topics available
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="p-4">
                    {isSearching ? (
                      <div className="flex items-center justify-center py-8">
                        <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                      </div>
                    ) : results.length > 0 ? (
                      <div className="space-y-2">
                        {results.map((result) => (
                          <button
                            key={result.id}
                            onClick={() => handleResultClick(result.slug, result.category)}
                            className="group flex w-full items-start gap-3 rounded-md p-3 text-left hover:bg-muted transition-colors"
                          >
                            <div className="flex-1">
                              <h4 className="font-medium group-hover:text-primary transition-colors">
                                {result.title}
                              </h4>
                              <p className="mt-1 text-sm text-muted-foreground line-clamp-2">
                                {result.excerpt}
                              </p>
                              <div className="mt-2 flex items-center gap-4 text-xs text-muted-foreground">
                                <span className="capitalize">{result.category}</span>
                                <span>{new Date(result.date).toLocaleDateString()}</span>
                              </div>
                            </div>
                            <ArrowRight className="h-5 w-5 text-muted-foreground group-hover:text-primary transition-colors mt-1" />
                          </button>
                        ))}
                      </div>
                    ) : (
                      <div className="py-8 text-center text-muted-foreground">
                        No results found for "{query}"
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}
