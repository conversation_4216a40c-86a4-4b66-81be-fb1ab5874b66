'use client';

import Link from 'next/link';
import { ReactNode, useCallback } from 'react';
import { useRouter } from 'next/navigation';

interface SubcategoryLinkProps {
  href: string;
  color: string;
  children: ReactNode;
}

export default function SubcategoryLink({ href, color, children }: SubcategoryLinkProps) {
  const router = useRouter();

  // Use a fixed set of classes based on the color prop
  const getColorClasses = (color: string) => {
    switch (color) {
      case 'blue':
        return 'bg-blue-100 hover:bg-blue-200 dark:bg-blue-900/20 dark:hover:bg-blue-900/30';
      case 'purple':
        return 'bg-purple-100 hover:bg-purple-200 dark:bg-purple-900/20 dark:hover:bg-purple-900/30';
      case 'red':
        return 'bg-red-100 hover:bg-red-200 dark:bg-red-900/20 dark:hover:bg-red-900/30';
      case 'green':
        return 'bg-green-100 hover:bg-green-200 dark:bg-green-900/20 dark:hover:bg-green-900/30';
      case 'yellow':
        return 'bg-yellow-100 hover:bg-yellow-200 dark:bg-yellow-900/20 dark:hover:bg-yellow-900/30';
      case 'pink':
        return 'bg-pink-100 hover:bg-pink-200 dark:bg-pink-900/20 dark:hover:bg-pink-900/30';
      default:
        return 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700';
    }
  };

  const className = `inline-flex text-xs rounded-full px-2.5 py-0.5 transition-colors cursor-pointer ${getColorClasses(color)}`;

  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering parent link
    e.preventDefault();
    router.push(href);
  }, [href, router]);

  // Use a span instead of a link to avoid nesting <a> tags
  return (
    <span
      className={className}
      onClick={handleClick}
      role="link"
      tabIndex={0}
    >
      {children}
    </span>
  );
}
