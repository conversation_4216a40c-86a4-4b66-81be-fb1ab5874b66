import Link from 'next/link'
import { X, <PERSON>edin, Github, Rss } from 'lucide-react'
import { Logo } from './logo'

const footerLinks = {
  categories: [
    { name: 'Technology', href: '/category/technology' },
    { name: 'Business', href: '/category/business' },
    { name: 'Health', href: '/category/health' },
    { name: 'Culture', href: '/category/culture' },
    { name: 'Finance', href: '/category/finance' },
    { name: 'Creative', href: '/category/creative' },
  ],
  company: [
    { name: 'About', href: '/about' },
    { name: 'Contact', href: '/contact' },
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' },
  ],
  resources: [
    { name: 'Newsletter', href: '/newsletter' },
    { name: 'RSS Feed', href: '/rss.xml' },
    { name: 'Archive', href: '/archive' },
    { name: 'Trending', href: '/trending' },
  ],
}

export function Footer() {
  return (
    <footer className="mt-16 border-t bg-muted/30 backdrop-blur-sm">
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-2 gap-8 md:grid-cols-4 md:gap-12">
          <div className="col-span-2 md:col-span-1">
            <Logo size="sm" className="mb-6" />
            <p className="text-sm text-muted-foreground mb-6 leading-relaxed">
              Discover tomorrow's trends today. Your premier destination for insights across technology, business, health, and culture.
            </p>
            <div className="flex space-x-6">
              <a
                href="https://twitter.com/duvainsight"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-primary transition-all duration-200 hover:scale-110"
                aria-label="Twitter"
              >
                <X className="h-6 w-6" />
              </a>
              <a
                href="https://linkedin.com/company/duvainsight"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-primary transition-all duration-200 hover:scale-110"
                aria-label="LinkedIn"
              >
                <Linkedin className="h-6 w-6" />
              </a>
              <a
                href="https://github.com/duvainsight"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-primary transition-all duration-200 hover:scale-110"
                aria-label="GitHub"
              >
                <Github className="h-6 w-6" />
              </a>
              <Link
                href="/rss.xml"
                className="text-muted-foreground hover:text-primary transition-all duration-200 hover:scale-110"
                aria-label="RSS Feed"
              >
                <Rss className="h-6 w-6" />
              </Link>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-6 text-foreground">Categories</h3>
            <ul className="space-y-3">
              {footerLinks.categories.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-sm text-muted-foreground hover:text-primary transition-all duration-200 hover:translate-x-1"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="font-semibold mb-6 text-foreground">Company</h3>
            <ul className="space-y-3">
              {footerLinks.company.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-sm text-muted-foreground hover:text-primary transition-all duration-200 hover:translate-x-1"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="font-semibold mb-6 text-foreground">Resources</h3>
            <ul className="space-y-3">
              {footerLinks.resources.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-sm text-muted-foreground hover:text-primary transition-all duration-200 hover:translate-x-1"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="mt-16 border-t border-border/50 pt-8">
          <p className="text-center text-base text-muted-foreground font-medium">
            © {new Date().getFullYear()} DuvaInsights. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  )
}
