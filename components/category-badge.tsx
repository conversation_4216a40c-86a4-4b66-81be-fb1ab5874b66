'use client';

import React from 'react';
import { Badge, BadgeProps } from '@/components/ui/badge';
import { cn } from '@/lib/shared/utils';
import { Sparkles } from 'lucide-react';
import {
  categoryColors,
  CategoryType,
  getCategoryColorClasses,
  getMainCategorySlug
} from '@/lib/shared/categoryColors';

export interface CategoryBadgeProps extends Omit<BadgeProps, 'variant'> {
  category?: string; // Accept any category or subcategory string
  subCategory?: string; // Optional subcategory
  showSparkle?: boolean;
  variant?: BadgeProps['variant'];
}

export function CategoryBadge({
  category,
  subCategory,
  className,
  showSparkle = false,
  children,
  ...props
}: CategoryBadgeProps) {
  // If no category is provided, fall back to the default badge
  if (!category && !subCategory) {
    return <Badge className={className} {...props}>{children}</Badge>;
  }

  // If subCategory is provided, use it to determine the color
  // Otherwise use the category
  const categoryToUse = subCategory || category;

  // Get color classes based on the main category of the provided category/subcategory
  const categoryColor = getCategoryColorClasses(categoryToUse || 'technology');

  return (
    <Badge
      variant="outline"
      className={cn(
        categoryColor.text,
        categoryColor.bg,
        categoryColor.border,
        categoryColor.hover,
        "flex items-center gap-1 transition-colors leading-normal",
        className
      )}
      {...props}
    >
      {showSparkle && <Sparkles className={`h-3 w-3 ${categoryColor.sparkle} align-middle`} />}
      <span className="align-middle">{children}</span>
    </Badge>
  );
}
