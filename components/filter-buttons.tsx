'use client';

import { Filter, SortAsc } from 'lucide-react';

export default function FilterButtons() {
  return (
    <div className="flex gap-2">
      <button 
        className="inline-flex items-center gap-2 rounded-md border px-3 py-1.5 text-sm hover:bg-muted transition-colors"
        onClick={() => console.log('Filter clicked')}
      >
        <Filter className="h-4 w-4" />
        Filter
      </button>
      <button 
        className="inline-flex items-center gap-2 rounded-md border px-3 py-1.5 text-sm hover:bg-muted transition-colors"
        onClick={() => console.log('Sort clicked')}
      >
        <SortAsc className="h-4 w-4" />
        Sort
      </button>
    </div>
  );
}
