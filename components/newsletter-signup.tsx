'use client'

import { useState } from 'react'
import { Mail, CheckCircle } from 'lucide-react'

export function NewsletterSignup() {
  const [email, setEmail] = useState('')
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!email) return

    setStatus('loading')

    // Simulate API call
    setTimeout(() => {
      setStatus('success')
      setEmail('')
      
      // Reset after 5 seconds
      setTimeout(() => {
        setStatus('idle')
      }, 5000)
    }, 1000)
  }

  return (
    <div className="rounded-lg bg-gradient-to-br from-primary/10 via-primary/5 to-primary/20 dark:from-slate-800 dark:via-slate-900 dark:to-slate-800 p-8 shadow-md border border-border/20 dark:border-white/5">
      <div className="mx-auto max-w-2xl">
        <div className="mb-6 text-center">
          <div className="mb-4 inline-flex rounded-full bg-primary/15 dark:bg-primary/30 p-4 shadow-sm">
            <Mail className="h-6 w-6 text-primary dark:text-primary-foreground" />
          </div>
          <h3 className="mb-3 text-2xl font-bold dark:text-white">Stay Ahead of the Curve</h3>
          <p className="text-muted-foreground dark:text-slate-300">
            Get weekly insights on emerging trends delivered straight to your inbox. 
            Join 50,000+ forward-thinking professionals.
          </p>
        </div>

        {status === 'success' ? (
          <div className="flex items-center justify-center gap-3 rounded-md bg-green-50 dark:bg-green-900/30 p-4 text-green-600 dark:text-green-300">
            <CheckCircle className="h-5 w-5" />
            <span className="font-medium">Success! Check your email to confirm your subscription.</span>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-3">
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email"
              className="flex-1 rounded-md border border-border/50 dark:border-white/10 bg-background dark:bg-slate-800 px-4 py-3 text-base placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background shadow-sm"
              required
            />
            <button
              type="submit"
              disabled={status === 'loading'}
              className="inline-flex items-center justify-center rounded-md bg-primary dark:bg-primary/90 px-6 py-3 text-sm font-medium text-primary-foreground hover:bg-primary/90 dark:hover:bg-primary transition-colors focus-ring disabled:opacity-50 disabled:cursor-not-allowed shadow-sm"
            >
              {status === 'loading' ? 'Subscribing...' : 'Subscribe'}
            </button>
          </form>
        )}

        <p className="mt-4 text-center text-xs text-muted-foreground dark:text-slate-400">
          We respect your privacy. Unsubscribe at any time.
        </p>
      </div>
    </div>
  )
}
