'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ChevronRight, Home } from 'lucide-react';
import { cn } from '@/lib/shared/utils';
import { mainCategories } from '@/lib/categoryMappings';
import { subCategoryToMainCategoryMap } from '@/lib/categoryMappings';

interface BreadcrumbProps {
  homeElement?: React.ReactNode;
  separator?: React.ReactNode;
  containerClassName?: string;
  listClassName?: string;
  activeItemClassName?: string;
  capitalizeItems?: boolean;
  documentTitle?: string; // Optional document title for post pages
}

interface BreadcrumbItem {
  label: string;
  url: string;
  isLast: boolean;
}

export function Breadcrumb({
  homeElement = <Home className="h-4 w-4" />,
  separator = <ChevronRight className="h-4 w-4 text-muted-foreground" />,
  containerClassName,
  listClassName,
  activeItemClassName,
  capitalizeItems = true,
  documentTitle,
}: BreadcrumbProps) {
  const pathname = usePathname();

  // Skip rendering breadcrumbs on homepage
  if (pathname === '/') {
    return null;
  }

  // Split the pathname into segments
  const segments = pathname
    .split('/')
    .filter(Boolean)
    .map((segment) => decodeURIComponent(segment));

  // Generate breadcrumb items
  const breadcrumbItems: BreadcrumbItem[] = segments.map((segment, index) => {
    // Build the URL for this segment
    const url = `/${segments.slice(0, index + 1).join('/')}`;

    // Format the label
    let label = segment;

    // Handle special cases
    if (segment === 'category' || segment === 'subcategory' || segment === 'post') {
      return null; // Skip these segments in the breadcrumb
    }

    // Check if this is a category slug
    const categoryName = Object.entries(mainCategories).find(
      ([_, slug]) => slug === segment
    )?.[0];

    if (categoryName) {
      label = categoryName;
    }

    // Check if this is a subcategory slug
    if (segments[index - 1] === 'subcategory') {
      // Format subcategory name from slug
      label = segment
        .split('-')
        .map(word => capitalizeItems ? word.charAt(0).toUpperCase() + word.slice(1) : word)
        .join(' ');
    }

    // For post detail pages
    if (segments[index - 2] === 'post') {
      // Use the provided document title if available, otherwise use the slug/ID
      if (documentTitle) {
        label = documentTitle.length > 40 ? documentTitle.substring(0, 40) + '...' : documentTitle;
      } else {
        // Fallback to slug/ID if no title provided
        if (label.length > 30) {
          label = label.substring(0, 30) + '...';
        }
      }
    }

    const isLast = index === segments.length - 1;

    return {
      label,
      url,
      isLast
    };
  }).filter(Boolean) as BreadcrumbItem[]; // Filter out null items and assert the type

  return (
    <nav aria-label="Breadcrumb" className={cn("flex bg-muted/30 rounded-lg px-3 py-2", containerClassName)}>
      <ol className={cn("flex items-center space-x-3", listClassName)}>
        {/* Home link */}
        <li className="flex items-center">
          <Link
            href="/"
            className="text-muted-foreground hover:text-primary transition-colors p-1 rounded hover:bg-background/50"
            aria-label="Home"
          >
            {homeElement}
          </Link>
        </li>

        {/* Separator after home */}
        {breadcrumbItems.length > 0 && (
          <li className="flex items-center">{separator}</li>
        )}

        {/* Breadcrumb items */}
        {breadcrumbItems.map((item, index) => (
          <React.Fragment key={item.url}>
            <li className="flex items-center">
              {item.isLast ? (
                <span
                  className={cn(
                    "text-sm font-semibold text-foreground px-2 py-1 bg-background/60 rounded",
                    activeItemClassName
                  )}
                  aria-current="page"
                >
                  {item.label}
                </span>
              ) : (
                <Link
                  href={item.url}
                  className="text-sm text-muted-foreground hover:text-primary transition-colors px-2 py-1 rounded hover:bg-background/50"
                >
                  {item.label}
                </Link>
              )}
            </li>

            {/* Separator between items */}
            {index < breadcrumbItems.length - 1 && (
              <li className="flex items-center">{separator}</li>
            )}
          </React.Fragment>
        ))}
      </ol>
    </nav>
  );
}
