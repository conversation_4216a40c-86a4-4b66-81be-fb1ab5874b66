import React from 'react';
import Link from 'next/link';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg'; // Different size options
  variant?: 'default' | 'minimal'; // Different variants
  className?: string;
}

export function Logo({ size = 'md', variant = 'default', className = '' }: LogoProps) {
  // Size mappings
  const sizeMap = {
    sm: {
      container: 'h-8',
      svg: 'h-8',
      text: 'text-lg',
    },
    md: {
      container: 'h-10',
      svg: 'h-10',
      text: 'text-xl',
    },
    lg: {
      container: 'h-12',
      svg: 'h-12',
      text: 'text-2xl',
    },
  };

  return (
    <Link href="/" className={`flex items-center gap-2 ${className}`}>
      <div className={`relative ${sizeMap[size].container} aspect-square`}>
        {/* SVG Logo Mark */}
        <svg
          className={`${sizeMap[size].svg} w-auto`}
          viewBox="0 0 50 50"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* Outer Circle - Light Ring */}
          <circle cx="25" cy="25" r="23" stroke="url(#outerGradient)" strokeWidth="2" />
          
          {/* Inner Circle - Filled */}
          <circle cx="25" cy="25" r="18" fill="url(#innerGradient)" />
          
          {/* Trend Lines */}
          <path
            d="M15 30L22 23L28 27L35 18"
            stroke="white"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          
          {/* Insight Dot - Pulse Animation */}
          <circle cx="35" cy="18" r="3" fill="white" className="animate-pulse" />
          
          {/* Gradients Definition */}
          <defs>
            <linearGradient id="outerGradient" x1="5" y1="5" x2="45" y2="45" gradientUnits="userSpaceOnUse">
              <stop offset="0%" stopColor="#3459D1" />
              <stop offset="50%" stopColor="#604DE2" />
              <stop offset="100%" stopColor="#873AE1" />
            </linearGradient>
            
            <linearGradient id="innerGradient" x1="10" y1="10" x2="40" y2="40" gradientUnits="userSpaceOnUse">
              <stop offset="0%" stopColor="#3459D1" />
              <stop offset="100%" stopColor="#873AE1" />
            </linearGradient>
          </defs>
        </svg>
      </div>
      
      {variant === 'default' && (
        <span className={`font-bold bg-gradient-to-r from-[#3459D1] via-[#604DE2] to-[#873AE1] bg-clip-text text-transparent ${sizeMap[size].text}`}>
          DuvaInsights
        </span>
      )}
    </Link>
  );
} 