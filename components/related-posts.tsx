import { ArticleCard } from './article-card'

// Mock related posts - in production, this would be fetched based on tags/category
const mockRelatedPosts = [
  {
    id: '5',
    title: 'Machine Learning in Healthcare: 2024 Breakthroughs',
    excerpt: 'How AI is revolutionizing diagnosis, treatment, and drug discovery in modern medicine.',
    category: 'technology',
    subCategory: 'ai-machine-learning',
    slug: 'ml-healthcare-2024',
    author: 'Dr. <PERSON>',
    date: '2024-01-10',
    readingTime: '7 min read',
  },
  {
    id: '6',
    title: 'The Ethics of AI: Navigating the Gray Areas',
    excerpt: 'As AI becomes more powerful, the ethical considerations become more complex and urgent.',
    category: 'technology',
    subCategory: 'ai-machine-learning',
    slug: 'ethics-of-ai',
    author: 'Prof. <PERSON>',
    date: '2024-01-08',
    readingTime: '9 min read',
  },
  {
    id: '7',
    title: 'Quantum Computing: From Theory to Practice',
    excerpt: 'Recent breakthroughs are bringing quantum computers closer to practical applications.',
    category: 'technology',
    subCategory: 'quantum-computing',
    slug: 'quantum-computing-practice',
    author: 'Dr. <PERSON>',
    date: '2024-01-05',
    readingTime: '11 min read',
  },
]

interface RelatedPostsProps {
  currentSlug: string
  category: string
  tags?: string[]
}

export function RelatedPosts({ currentSlug, category, tags }: RelatedPostsProps) {
  // Filter out the current post
  const relatedPosts = mockRelatedPosts.filter(post => post.slug !== currentSlug)

  return (
    <div>
      <h2 className="mb-8 text-center text-3xl font-bold">Related Articles</h2>
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {relatedPosts.slice(0, 3).map((post) => (
          <ArticleCard key={post.id} article={post} />
        ))}
      </div>
    </div>
  )
}
