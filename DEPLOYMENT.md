# DuvaInsight - Production Deployment Guide

## 🚀 Quick Start

1. **Install Dependencies**
   ```bash
   cd /Users/<USER>/Claude-folder-permission/duvainsight
   npm install
   ```

2. **Run Development Server**
   ```bash
   npm run dev
   ```
   Visit http://localhost:3000

## 📁 Project Structure Overview

```
duvainsight/
├── app/                    # Next.js 15 App Router
│   ├── layout.tsx         # Root layout with theme provider
│   ├── page.tsx           # Homepage
│   ├── globals.css        # Global styles & Tailwind
│   ├── post/              # Dynamic article pages
│   ├── category/          # Category listings
│   ├── newsletter/        # Newsletter signup
│   ├── about/             # About page
│   ├── archive/           # Article archive with search
│   ├── trending/          # Trending topics
│   ├── contact/           # Contact form
│   └── privacy/           # Privacy policy
├── components/            # Reusable React components
│   ├── header.tsx         # Site navigation
│   ├── footer.tsx         # Site footer
│   ├── article-card.tsx   # Article preview cards
│   ├── search-modal.tsx   # Full-text search
│   └── ...                # Other components
├── content/               # MDX blog posts
│   └── technology/        # Sample article
├── lib/                   # Utility functions
├── public/                # Static assets
└── Configuration Files
    ├── next.config.mjs    # Next.js config with MDX
    ├── tailwind.config.js # Tailwind CSS config
    ├── tsconfig.json      # TypeScript config
    └── package.json       # Dependencies & scripts
```

## 🎯 Key Features Implemented

### Content Management
- ✅ MDX support for rich article content
- ✅ Category-based organization
- ✅ Author profiles
- ✅ Related articles
- ✅ Table of contents for long articles

### User Experience
- ✅ Dark/Light mode toggle
- ✅ Responsive design (mobile-first)
- ✅ Real-time search with Fuse.js
- ✅ Newsletter subscription
- ✅ Social sharing buttons
- ✅ Reading time estimates

### SEO & Performance
- ✅ Static site generation (SSG)
- ✅ SEO meta tags
- ✅ Sitemap generation
- ✅ Robots.txt
- ✅ Open Graph tags
- ✅ Loading states
- ✅ Error boundaries

### Developer Experience
- ✅ TypeScript for type safety
- ✅ ESLint configuration
- ✅ Prettier formatting
- ✅ GitHub Actions CI/CD
- ✅ Environment variables setup

## 🚢 Deployment Steps

### 1. Vercel (Recommended)

1. Push to GitHub:
   ```bash
   git init
   git add .
   git commit -m "Initial commit"
   git remote add origin YOUR_GITHUB_REPO_URL
   git push -u origin main
   ```

2. Import to Vercel:
   - Go to https://vercel.com/new
   - Import your GitHub repository
   - Configure environment variables (if any)
   - Deploy

### 2. Static Export

For static hosting (Netlify, GitHub Pages, etc.):
```bash
npm run build:static
```
Upload the `out/` directory to your hosting provider.

## 📝 Adding Content

### Create New Article

1. Create MDX file in appropriate category:
   ```
   content/[category]/your-article-slug.mdx
   ```

2. Add frontmatter:
   ```yaml
   ---
   title: "Your Article Title"
   excerpt: "Brief description"
   author: "Author Name"
   date: "2024-01-20"
   category: "technology"
   tags: ["tag1", "tag2"]
   readingTime: "5 min"
   ---
   ```

3. Write content using Markdown and MDX components

### Add New Category

1. Create folder in `content/`
2. Update category lists in:
   - `components/header.tsx`
   - `components/footer.tsx`
   - `components/category-grid.tsx`
   - `app/category/[slug]/page.tsx`

## 🔧 Customization

### Theme Colors
Edit CSS variables in `app/globals.css`:
```css
:root {
  --primary: 220.9 39.3% 11%;
  /* Update other color variables */
}
```

### Site Metadata
Update in `app/layout.tsx`:
- Site title
- Description
- Social media links
- OG images

### Analytics
Add tracking codes to `.env.local`:
```
NEXT_PUBLIC_GA_ID=your-ga-id
NEXT_PUBLIC_GTM_ID=your-gtm-id
```

## 🎨 Design System

### Colors
- Primary: Blue/Purple gradient
- Background: White/Dark adaptive
- Accents: Category-specific colors

### Typography
- Font: Inter (Google Fonts)
- Responsive sizing
- Optimized line heights

### Components
- Consistent border radius
- Subtle shadows
- Smooth transitions
- Focus states for accessibility

## 📊 Performance Optimization

- Images: Use Next.js Image component
- Fonts: Optimized loading with next/font
- Code splitting: Automatic with App Router
- Caching: Static pages cached at edge

## 🐛 Troubleshooting

### Common Issues

1. **Module not found errors**
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **TypeScript errors**
   ```bash
   npm run type-check
   ```

3. **Build failures**
   Check for:
   - Missing environment variables
   - Invalid MDX syntax
   - Import path issues

## 📚 Next Steps

1. **Content Strategy**
   - Create editorial calendar
   - Set up content workflow
   - Recruit contributors

2. **Features to Add**
   - Comments system
   - User accounts
   - Analytics dashboard
   - A/B testing

3. **Monetization**
   - Ad integration
   - Premium subscriptions
   - Sponsored content

4. **Marketing**
   - SEO optimization
   - Social media presence
   - Email campaigns

## 🤝 Support

- Documentation: Check README.md
- Issues: Create GitHub issue
- Email: <EMAIL>

---

Built with ❤️ using Next.js 15, React 19, and Tailwind CSS
