// lib/supabase.ts
import { createClient } from '@supabase/supabase-js';

// For production, we'll use a mock client that returns mock data
// This prevents errors when environment variables aren't available
const createMockClient = () => {
  // Mock data for documents
  const mockDocuments = [
    {
      id: 'mock-1',
      title: 'The Rise of AI-Powered Personal Assistants',
      metadata: {
        metadata: {
          primary_objective: 'How artificial intelligence is revolutionizing personal productivity and daily life',
          category: 'ai-machine-learning',
          classification: 'NON-TECHNICAL',
          author: 'AI Research Team'
        }
      },
      created_at: new Date().toISOString(),
      image_url: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=450&fit=crop'
    },
    {
      id: 'mock-2',
      title: 'Web Development Trends for 2024',
      metadata: {
        metadata: {
          primary_objective: 'Exploring the latest frameworks, tools, and methodologies shaping modern web development',
          category: 'web-mobile-development',
          classification: 'TECHNICAL',
          author: 'Web Dev Insights'
        }
      },
      created_at: new Date().toISOString(),
      image_url: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=800&h=450&fit=crop'
    },
    {
      id: 'mock-3',
      title: 'Cybersecurity Best Practices for Remote Teams',
      metadata: {
        metadata: {
          primary_objective: 'Essential security measures for protecting distributed workforce and company data',
          category: 'cybersecurity-privacy',
          classification: 'NON-TECHNICAL',
          author: 'Security Expert'
        }
      },
      created_at: new Date().toISOString(),
      image_url: 'https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=800&h=450&fit=crop'
    }
  ];

  return {
    from: (table: string) => ({
      select: () => ({
        eq: () => ({
          single: async () => {
            // Return the first mock document for single queries
            return { data: mockDocuments[0], error: null };
          },
          order: () => ({
            limit: async () => ({ data: mockDocuments, error: null })
          }),
          async then() {
            return { data: mockDocuments, error: null };
          }
        }),
        order: () => ({
          async then() {
            return { data: mockDocuments, error: null };
          }
        }),
        async then() {
          return { data: mockDocuments, error: null };
        }
      })
    })
  };
};

// Use real Supabase client if environment variables are available, otherwise use mock
export const supabase = process.env.SUPABASE_URL && process.env.SUPABASE_ANON_KEY
  ? createClient(process.env.SUPABASE_URL, process.env.SUPABASE_ANON_KEY)
  : createMockClient() as any;