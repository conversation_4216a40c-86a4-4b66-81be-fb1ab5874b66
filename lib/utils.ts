import { clsx, type ClassValue } from 'clsx'

export function cn(...inputs: ClassValue[]) {
  return clsx(inputs)
}

// Generate URL-friendly slug from title
export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim()
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
}

// Create clean SEO-friendly slug from title only
export function createSEOSlug(title: string): string {
  const baseSlug = generateSlug(title)
  return baseSlug || 'untitled'
}

// Create slug with conflict resolution
export function createSlugWithConflictResolution(title: string, id: string, existingSlugs: string[]): string {
  const baseSlug = createSEOSlug(title)

  // If no conflict, return the base slug
  if (!existingSlugs.includes(baseSlug)) {
    return baseSlug
  }

  // If conflict exists, append a short version of the ID
  const shortId = id.slice(-8) // Last 8 characters of ID
  return `${baseSlug}-${shortId}`
}

// Keep the old function for backward compatibility during transition
export function createUniqueSlug(title: string, id: string): string {
  return createSEOSlug(title)
}
