import { supabase } from './supabase';
import { getMainCategoryFromSubCategory } from './categoryMappings';
import { createUniqueSlug } from './utils';

// Re-export the function from categoryMappings
export { getMainCategoryFromSubCategory };

// Fetch documents by sub-category
export async function getDocumentsBySubCategory(subCategorySlug: string) {
  const { data, error } = await supabase
    .from('sop_documents')
    .select(`
      id,
      user_id,
      metadata,
      pipeline_outputs,
      created_at,
      updated_at
    `)
    .eq('metadata->metadata->category', subCategorySlug);

  if (error || !data || data.length === 0) {
    console.log('No documents found in Supabase for subcategory:', subCategorySlug);

    // Return hardcoded documents filtered by subcategory
    const hardcodedDocs = [
      {
        id: 'hardcoded-1',
        title: 'The Rise of AI-Powered Personal Assistants',
        subCategory: 'ai-machine-learning',
        mainCategory: 'Technology',
        mainCategorySlug: 'technology',
        classification: 'NON-TECHNICAL',
        type: 'non-technical',
        metadata: {
          metadata: {
            title: 'The Rise of AI-Powered Personal Assistants',
            primary_objective: 'How artificial intelligence is revolutionizing personal productivity and daily life',
            author: 'AI Research Team',
            category: 'ai-machine-learning'
          }
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: 'system'
      },
      {
        id: 'hardcoded-2',
        title: 'Web Development Trends for 2024',
        subCategory: 'web-mobile-development',
        mainCategory: 'Technology',
        mainCategorySlug: 'technology',
        classification: 'TECHNICAL',
        type: 'technical',
        metadata: {
          metadata: {
            title: 'Web Development Trends for 2024',
            primary_objective: 'Exploring the latest frameworks, tools, and methodologies shaping modern web development',
            author: 'Web Dev Insights',
            category: 'web-mobile-development'
          }
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: 'system'
      },
      {
        id: 'hardcoded-3',
        title: 'Cybersecurity Best Practices for Remote Teams',
        subCategory: 'cybersecurity-privacy',
        mainCategory: 'Technology',
        mainCategorySlug: 'technology',
        classification: 'NON-TECHNICAL',
        type: 'non-technical',
        metadata: {
          metadata: {
            title: 'Cybersecurity Best Practices for Remote Teams',
            primary_objective: 'Essential security measures for protecting distributed workforce and company data',
            author: 'Security Expert',
            category: 'cybersecurity-privacy'
          }
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: 'system'
      }
    ];

    return hardcodedDocs
      .filter(doc => doc.subCategory === subCategorySlug)
      .map(doc => ({
        ...doc,
        type: doc.classification?.toLowerCase() === 'technical' ? 'technical' : 'non-technical'
      })) as SopDocument[];
  }

  return data.map((doc: any) => {
    // Get the subcategory from the document metadata
    const subCategory = doc.metadata?.metadata?.category || 'ai-machine-learning';

    // Get the main category from the subcategory using the mapping
    const mainCategorySlug = getMainCategoryFromSubCategory(subCategory) || 'technology';

    // Get the main category name (capitalize first letter)
    const mainCategory = mainCategorySlug.charAt(0).toUpperCase() + mainCategorySlug.slice(1);

    // Generate title and slug
    const title = doc.metadata?.metadata?.title || 'Untitled Document';
    const slug = createUniqueSlug(title, doc.id);

    return {
      ...doc,
      title,
      slug,
      subCategory,
      mainCategory,
      mainCategorySlug,
      type: doc.metadata?.metadata?.classification?.toLowerCase() === 'technical' ? 'technical' : 'non-technical'
    };
  }) as SopDocument[];
}

// Define the SopDocument type to fix type issues
export interface SopDocument {
  id: string;
  title: string;
  slug: string;
  subCategory: string;
  mainCategory: string;
  mainCategorySlug: string;
  classification: string;
  metadata: any;
  createdAt: string;
  type: string;
  user_id?: string;
  pipeline_outputs?: any;
  created_at?: string;
  updated_at?: string;
}

// Fetch a single document by slug (which contains the ID at the end)
export async function getDocumentBySlug(slug: string, _mainCategorySlug: string) {
  // Extract ID from slug (slug format: "title-words-ID")
  const parts = slug.split('-');
  const id = parts[parts.length - 1];

  return getDocumentByIdAndCategory(id, _mainCategorySlug);
}

// Fetch a single document by ID and validate its category hierarchy
export async function getDocumentByIdAndCategory(id: string, _mainCategorySlug: string, subCategorySlug?: string) {
  const { data, error } = await supabase
    .from('sop_documents')
    .select(`
      id,
      user_id,
      metadata,
      pipeline_outputs,
      created_at,
      updated_at
    `)
    .eq('id', id)
    .single();

  if (error || !data) {
    console.log('No document found in Supabase, checking hardcoded data');

    // Check if the ID matches one of our hardcoded documents
    if (id.startsWith('hardcoded-')) {
      const hardcodedDocs = [
        {
          id: 'hardcoded-1',
          title: 'The Rise of AI-Powered Personal Assistants',
          slug: createUniqueSlug('The Rise of AI-Powered Personal Assistants', 'hardcoded-1'),
          subCategory: 'ai-machine-learning',
          mainCategory: 'Technology',
          mainCategorySlug: getMainCategoryFromSubCategory('ai-machine-learning') || 'technology',
          classification: 'NON-TECHNICAL',
          type: 'non-technical',
          metadata: {
            metadata: {
              title: 'The Rise of AI-Powered Personal Assistants',
              primary_objective: 'How artificial intelligence is revolutionizing personal productivity and daily life',
              author: 'AI Research Team',
              category: 'ai-machine-learning'
            }
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          user_id: 'system',
          pipeline_outputs: {}
        },
        {
          id: 'hardcoded-2',
          title: 'Web Development Trends for 2024',
          slug: createUniqueSlug('Web Development Trends for 2024', 'hardcoded-2'),
          subCategory: 'web-mobile-development',
          mainCategory: 'Technology',
          mainCategorySlug: getMainCategoryFromSubCategory('web-mobile-development') || 'technology',
          classification: 'TECHNICAL',
          type: 'technical',
          metadata: {
            metadata: {
              title: 'Web Development Trends for 2024',
              primary_objective: 'Exploring the latest frameworks, tools, and methodologies shaping modern web development',
              author: 'Web Dev Insights',
              category: 'web-mobile-development'
            }
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          user_id: 'system',
          pipeline_outputs: {}
        },
        {
          id: 'hardcoded-3',
          title: 'Cybersecurity Best Practices for Remote Teams',
          slug: createUniqueSlug('Cybersecurity Best Practices for Remote Teams', 'hardcoded-3'),
          subCategory: 'cybersecurity-privacy',
          mainCategory: 'Technology',
          mainCategorySlug: getMainCategoryFromSubCategory('cybersecurity-privacy') || 'technology',
          classification: 'NON-TECHNICAL',
          type: 'non-technical',
          metadata: {
            metadata: {
              title: 'Cybersecurity Best Practices for Remote Teams',
              primary_objective: 'Essential security measures for protecting distributed workforce and company data',
              author: 'Security Expert',
              category: 'cybersecurity-privacy'
            }
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          user_id: 'system',
          pipeline_outputs: {}
        },
        {
          id: 'hardcoded-4',
          title: 'Building a Directory Website Using AI',
          slug: createUniqueSlug('Building a Directory Website Using AI', 'hardcoded-4'),
          subCategory: 'ai-machine-learning',
          mainCategory: 'Technology',
          mainCategorySlug: getMainCategoryFromSubCategory('ai-machine-learning') || 'technology',
          classification: 'TECHNICAL',
          type: 'technical',
          metadata: {
            metadata: {
              title: 'Building a Directory Website Using AI',
              primary_objective: 'This technical summary covers the process of building a directory website from scratch using AI tools',
              author: 'Tech Team',
              category: 'ai-machine-learning'
            }
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          user_id: 'system',
          pipeline_outputs: {}
        },
        {
          id: 'hardcoded-5',
          title: 'Leadership Strategies for Remote Teams',
          slug: createUniqueSlug('Leadership Strategies for Remote Teams', 'hardcoded-5'),
          subCategory: 'leadership-management',
          mainCategory: 'Business',
          mainCategorySlug: 'business',
          classification: 'NON-TECHNICAL',
          type: 'non-technical',
          metadata: {
            metadata: {
              title: 'Leadership Strategies for Remote Teams',
              primary_objective: 'Effective approaches to managing and motivating distributed teams',
              author: 'Management Expert',
              category: 'leadership-management'
            }
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          user_id: 'system',
          pipeline_outputs: {}
        },
        {
          id: 'hardcoded-6',
          title: 'Digital Marketing Trends for 2024',
          slug: createUniqueSlug('Digital Marketing Trends for 2024', 'hardcoded-6'),
          subCategory: 'marketing-sales',
          mainCategory: 'Business',
          mainCategorySlug: 'business',
          classification: 'NON-TECHNICAL',
          type: 'non-technical',
          metadata: {
            metadata: {
              title: 'Digital Marketing Trends for 2024',
              primary_objective: 'Emerging strategies and technologies reshaping the digital marketing landscape',
              author: 'Marketing Strategist',
              category: 'marketing-sales'
            }
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          user_id: 'system',
          pipeline_outputs: {}
        },
        {
          id: 'hardcoded-7',
          title: 'Wellness Practices for Tech Professionals',
          slug: createUniqueSlug('Wellness Practices for Tech Professionals', 'hardcoded-7'),
          subCategory: 'wellness-fitness',
          mainCategory: 'Health',
          mainCategorySlug: 'health',
          classification: 'NON-TECHNICAL',
          type: 'non-technical',
          metadata: {
            metadata: {
              title: 'Wellness Practices for Tech Professionals',
              primary_objective: 'Strategies to maintain physical and mental health in high-stress tech environments',
              author: 'Wellness Coach',
              category: 'wellness-fitness'
            }
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          user_id: 'system',
          pipeline_outputs: {}
        },
        {
          id: 'hardcoded-8',
          title: 'Cryptocurrency Investment Strategies',
          slug: createUniqueSlug('Cryptocurrency Investment Strategies', 'hardcoded-8'),
          subCategory: 'cryptocurrency',
          mainCategory: 'Finance',
          mainCategorySlug: 'finance',
          classification: 'NON-TECHNICAL',
          type: 'non-technical',
          metadata: {
            metadata: {
              title: 'Cryptocurrency Investment Strategies',
              primary_objective: 'Risk management and portfolio diversification approaches for crypto investors',
              author: 'Crypto Analyst',
              category: 'cryptocurrency'
            }
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          user_id: 'system',
          pipeline_outputs: {}
        },
        {
          id: 'hardcoded-9',
          title: 'The Evolution of Social Media Culture',
          slug: createUniqueSlug('The Evolution of Social Media Culture', 'hardcoded-9'),
          subCategory: 'social-trends',
          mainCategory: 'Culture',
          mainCategorySlug: 'culture',
          classification: 'NON-TECHNICAL',
          type: 'non-technical',
          metadata: {
            metadata: {
              title: 'The Evolution of Social Media Culture',
              primary_objective: 'How social platforms are reshaping communication, relationships, and society',
              author: 'Cultural Researcher',
              category: 'social-trends'
            }
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          user_id: 'system',
          pipeline_outputs: {}
        },
        {
          id: 'hardcoded-10',
          title: 'UX Design Principles for Mobile Apps',
          slug: createUniqueSlug('UX Design Principles for Mobile Apps', 'hardcoded-10'),
          subCategory: 'design',
          mainCategory: 'Creative',
          mainCategorySlug: 'creative',
          classification: 'TECHNICAL',
          type: 'technical',
          metadata: {
            metadata: {
              title: 'UX Design Principles for Mobile Apps',
              primary_objective: 'Best practices for creating intuitive and engaging mobile user experiences',
              author: 'UX Designer',
              category: 'design'
            }
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          user_id: 'system',
          pipeline_outputs: {}
        },
        {
          id: 'hardcoded-11',
          title: 'Content Creation Strategies for 2024',
          slug: createUniqueSlug('Content Creation Strategies for 2024', 'hardcoded-11'),
          subCategory: 'writing',
          mainCategory: 'Creative',
          mainCategorySlug: 'creative',
          classification: 'NON-TECHNICAL',
          type: 'non-technical',
          metadata: {
            metadata: {
              title: 'Content Creation Strategies for 2024',
              primary_objective: 'Innovative approaches to developing engaging content across different platforms',
              author: 'Content Strategist',
              category: 'writing'
            }
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          user_id: 'system',
          pipeline_outputs: {}
        },
        {
          id: 'hardcoded-12',
          title: 'Sustainable Business Practices',
          slug: createUniqueSlug('Sustainable Business Practices', 'hardcoded-12'),
          subCategory: 'strategy-planning',
          mainCategory: 'Business',
          mainCategorySlug: 'business',
          classification: 'NON-TECHNICAL',
          type: 'non-technical',
          metadata: {
            metadata: {
              title: 'Sustainable Business Practices',
              primary_objective: 'Implementing eco-friendly strategies that benefit both the environment and the bottom line',
              author: 'Sustainability Consultant',
              category: 'strategy-planning'
            }
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          user_id: 'system',
          pipeline_outputs: {}
        }
      ];

      const doc = hardcodedDocs.find(doc => doc.id === id);
      if (doc) {
        console.log('Found hardcoded document:', doc.id);
        return doc;
      }
    }

    // If no hardcoded document matches, return null
    return null;
  }

  // Get the document's sub-category
  const docSubCategory = data.metadata?.metadata?.category;

  // If a specific subcategory was provided, validate it
  if (subCategorySlug && docSubCategory !== subCategorySlug) {
    return null;
  }

  // Determine technical vs non-technical for tabs
  const classif = data.metadata?.metadata?.classification?.toLowerCase();
  const type = classif === 'technical' ? 'technical' : 'non-technical';

  // Get the subcategory from the document metadata
  const subCategory = data.metadata?.metadata?.category || 'ai-machine-learning';

  // Get the main category from the subcategory using the mapping
  const documentMainCategorySlug = getMainCategoryFromSubCategory(subCategory) || 'technology';

  // Get the main category name (capitalize first letter)
  const mainCategory = documentMainCategorySlug.charAt(0).toUpperCase() + documentMainCategorySlug.slice(1);

  // Generate title and slug
  const title = data.metadata?.metadata?.title || 'Untitled Document';
  const slug = createUniqueSlug(title, data.id);

  return {
    ...data,
    title,
    slug,
    type,
    subCategory,
    mainCategory,
    mainCategorySlug: documentMainCategorySlug
  } as SopDocument;
}

// Get all documents for the main page
export async function getAllDocuments() {
  const { data, error } = await supabase
    .from('sop_documents')
    .select(`
      id,
      metadata,
      created_at
    `)
    .order('created_at', { ascending: false });

  if (error || !data || data.length === 0) {
    console.log('No data from Supabase, creating hardcoded data for search');

    // Create hardcoded data for search functionality
    // Map each document's subcategory to its main category using the mapping
    return [
      {
        id: 'hardcoded-1',
        title: 'The Rise of AI-Powered Personal Assistants',
        slug: createUniqueSlug('The Rise of AI-Powered Personal Assistants', 'hardcoded-1'),
        subCategory: 'ai-machine-learning',
        mainCategory: 'Technology',
        mainCategorySlug: getMainCategoryFromSubCategory('ai-machine-learning') || 'technology',
        classification: 'NON-TECHNICAL',
        metadata: {
          metadata: {
            title: 'The Rise of AI-Powered Personal Assistants',
            primary_objective: 'How artificial intelligence is revolutionizing personal productivity and daily life',
            author: 'AI Research Team',
            category: 'ai-machine-learning'
          }
        },
        createdAt: new Date().toISOString()
      },
      {
        id: 'hardcoded-2',
        title: 'Web Development Trends for 2024',
        slug: createUniqueSlug('Web Development Trends for 2024', 'hardcoded-2'),
        subCategory: 'web-mobile-development',
        mainCategory: 'Technology',
        mainCategorySlug: getMainCategoryFromSubCategory('web-mobile-development') || 'technology',
        classification: 'TECHNICAL',
        metadata: {
          metadata: {
            title: 'Web Development Trends for 2024',
            primary_objective: 'Exploring the latest frameworks, tools, and methodologies shaping modern web development',
            author: 'Web Dev Insights',
            category: 'web-mobile-development'
          }
        },
        createdAt: new Date().toISOString()
      },
      {
        id: 'hardcoded-3',
        title: 'Cybersecurity Best Practices for Remote Teams',
        subCategory: 'cybersecurity-privacy',
        mainCategory: 'Technology',
        mainCategorySlug: getMainCategoryFromSubCategory('cybersecurity-privacy') || 'technology',
        classification: 'NON-TECHNICAL',
        metadata: {
          metadata: {
            title: 'Cybersecurity Best Practices for Remote Teams',
            primary_objective: 'Essential security measures for protecting distributed workforce and company data',
            author: 'Security Expert',
            category: 'cybersecurity-privacy'
          }
        },
        createdAt: new Date().toISOString()
      },
      {
        id: 'hardcoded-4',
        title: 'Building a Directory Website Using AI',
        subCategory: 'ai-machine-learning',
        mainCategory: 'Technology',
        mainCategorySlug: getMainCategoryFromSubCategory('ai-machine-learning') || 'technology',
        classification: 'TECHNICAL',
        metadata: {
          metadata: {
            title: 'Building a Directory Website Using AI',
            primary_objective: 'This technical summary covers the process of building a directory website from scratch using AI tools',
            author: 'Tech Team',
            category: 'ai-machine-learning'
          }
        },
        createdAt: new Date().toISOString()
      },
      {
        id: 'hardcoded-5',
        title: 'Leadership Strategies for Remote Teams',
        subCategory: 'leadership-management',
        mainCategory: 'Business',
        mainCategorySlug: 'business',
        classification: 'NON-TECHNICAL',
        metadata: {
          metadata: {
            title: 'Leadership Strategies for Remote Teams',
            primary_objective: 'Effective approaches to managing and motivating distributed teams',
            author: 'Management Expert',
            category: 'leadership-management'
          }
        },
        createdAt: new Date().toISOString()
      },
      {
        id: 'hardcoded-6',
        title: 'Digital Marketing Trends for 2024',
        subCategory: 'marketing-sales',
        mainCategory: 'Business',
        mainCategorySlug: 'business',
        classification: 'NON-TECHNICAL',
        metadata: {
          metadata: {
            title: 'Digital Marketing Trends for 2024',
            primary_objective: 'Emerging strategies and technologies reshaping the digital marketing landscape',
            author: 'Marketing Strategist',
            category: 'marketing-sales'
          }
        },
        createdAt: new Date().toISOString()
      },
      {
        id: 'hardcoded-7',
        title: 'Wellness Practices for Tech Professionals',
        subCategory: 'wellness-fitness',
        mainCategory: 'Health',
        mainCategorySlug: 'health',
        classification: 'NON-TECHNICAL',
        metadata: {
          metadata: {
            title: 'Wellness Practices for Tech Professionals',
            primary_objective: 'Strategies to maintain physical and mental health in high-stress tech environments',
            author: 'Wellness Coach',
            category: 'wellness-fitness'
          }
        },
        createdAt: new Date().toISOString()
      },
      {
        id: 'hardcoded-8',
        title: 'Cryptocurrency Investment Strategies',
        subCategory: 'cryptocurrency',
        mainCategory: 'Finance',
        mainCategorySlug: 'finance',
        classification: 'NON-TECHNICAL',
        metadata: {
          metadata: {
            title: 'Cryptocurrency Investment Strategies',
            primary_objective: 'Risk management and portfolio diversification approaches for crypto investors',
            author: 'Crypto Analyst',
            category: 'cryptocurrency'
          }
        },
        createdAt: new Date().toISOString()
      },
      {
        id: 'hardcoded-9',
        title: 'The Evolution of Social Media Culture',
        subCategory: 'social-trends',
        mainCategory: 'Culture',
        mainCategorySlug: 'culture',
        classification: 'NON-TECHNICAL',
        metadata: {
          metadata: {
            title: 'The Evolution of Social Media Culture',
            primary_objective: 'How social platforms are reshaping communication, relationships, and society',
            author: 'Cultural Researcher',
            category: 'social-trends'
          }
        },
        createdAt: new Date().toISOString()
      },
      {
        id: 'hardcoded-10',
        title: 'UX Design Principles for Mobile Apps',
        subCategory: 'design',
        mainCategory: 'Creative',
        mainCategorySlug: 'creative',
        classification: 'TECHNICAL',
        metadata: {
          metadata: {
            title: 'UX Design Principles for Mobile Apps',
            primary_objective: 'Best practices for creating intuitive and engaging mobile user experiences',
            author: 'UX Designer',
            category: 'design'
          }
        },
        createdAt: new Date().toISOString()
      },
      {
        id: 'hardcoded-11',
        title: 'Content Creation Strategies for 2024',
        subCategory: 'writing',
        mainCategory: 'Creative',
        mainCategorySlug: 'creative',
        classification: 'NON-TECHNICAL',
        metadata: {
          metadata: {
            title: 'Content Creation Strategies for 2024',
            primary_objective: 'Innovative approaches to developing engaging content across different platforms',
            author: 'Content Strategist',
            category: 'writing'
          }
        },
        createdAt: new Date().toISOString()
      },
      {
        id: 'hardcoded-12',
        title: 'Sustainable Business Practices',
        subCategory: 'strategy-planning',
        mainCategory: 'Business',
        mainCategorySlug: 'business',
        classification: 'NON-TECHNICAL',
        metadata: {
          metadata: {
            title: 'Sustainable Business Practices',
            primary_objective: 'Implementing eco-friendly strategies that benefit both the environment and the bottom line',
            author: 'Sustainability Consultant',
            category: 'strategy-planning'
          }
        },
        createdAt: new Date().toISOString()
      }
    ];
  }

  const processedDocs = data.map((doc: any) => {
    // Get the subcategory from the document metadata
    const subCategory = doc.metadata?.metadata?.category || 'ai-machine-learning';

    // Get the main category from the subcategory using the mapping
    const mainCategorySlug = getMainCategoryFromSubCategory(subCategory) || 'technology';

    // Get the main category name (capitalize first letter)
    const mainCategory = mainCategorySlug.charAt(0).toUpperCase() + mainCategorySlug.slice(1);

    return {
      id: doc.id,
      title: doc.metadata?.metadata?.title || 'Untitled',
      subCategory: subCategory,
      mainCategory: mainCategory,
      mainCategorySlug: mainCategorySlug,
      classification: doc.metadata?.metadata?.classification || 'NON-TECHNICAL',
      metadata: doc.metadata,
      createdAt: doc.created_at
    };
  });

  return processedDocs;
}


