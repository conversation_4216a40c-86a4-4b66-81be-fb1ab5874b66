import { supabase } from './supabase';
import { getMainCategoryFromSubCategory } from './categoryMappings';
import { createSEOSlug } from './utils';

// Re-export the function from categoryMappings
export { getMainCategoryFromSubCategory };

// Fetch documents by sub-category
export async function getDocumentsBySubCategory(subCategorySlug: string) {
  const { data, error } = await supabase
    .from('sop_documents')
    .select(`
      id,
      user_id,
      metadata,
      pipeline_outputs,
      created_at,
      updated_at,
      image_url
    `)
    .eq('metadata->metadata->category', subCategorySlug);

  if (error) {
    console.error('Error fetching documents by subcategory:', error);
    return [];
  }

  if (!data || data.length === 0) {
    console.log('No documents found for subcategory:', subCategorySlug);
    return [];
  }

  return data.map((doc: any) => {
    // Get the subcategory from the document metadata
    const subCategory = doc.metadata?.metadata?.category || 'ai-machine-learning';

    // Get the main category from the subcategory using the mapping
    const mainCategorySlug = getMainCategoryFromSubCategory(subCategory) || 'technology';

    // Get the main category name (capitalize first letter)
    const mainCategory = mainCategorySlug.charAt(0).toUpperCase() + mainCategorySlug.slice(1);

    // Generate title and slug
    const title = doc.metadata?.metadata?.title || 'Untitled Document';
    const slug = createSEOSlug(title);

    return {
      ...doc,
      title,
      slug,
      subCategory,
      mainCategory,
      mainCategorySlug,
      type: doc.metadata?.metadata?.classification?.toLowerCase() === 'technical' ? 'technical' : 'non-technical'
    };
  }) as SopDocument[];
}

// Define the SopDocument type to fix type issues
export interface SopDocument {
  id: string;
  title: string;
  slug: string;
  subCategory: string;
  mainCategory: string;
  mainCategorySlug: string;
  classification: string;
  metadata: any;
  createdAt: string;
  type: string;
  user_id?: string;
  pipeline_outputs?: any;
  created_at?: string;
  updated_at?: string;
  image_url?: string;
}

// Fetch a single document by slug (clean title-based slug)
export async function getDocumentBySlug(slug: string, _mainCategorySlug: string) {
  // First, get all documents and find the one with matching slug
  const allDocuments = await getAllDocuments();

  // Find document with matching slug
  const document = allDocuments.find((doc: SopDocument) => doc.slug === slug);

  if (!document) {
    return null;
  }

  // Return the full document data by fetching it by ID
  return getDocumentByIdAndCategory(document.id, _mainCategorySlug);
}

// Fetch a single document by ID and validate its category hierarchy
export async function getDocumentByIdAndCategory(id: string, _mainCategorySlug: string, subCategorySlug?: string) {
  const { data, error } = await supabase
    .from('sop_documents')
    .select(`
      id,
      user_id,
      metadata,
      pipeline_outputs,
      created_at,
      updated_at,
      image_url
    `)
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching document by ID:', error);
    return null;
  }

  if (!data) {
    console.log('No document found with ID:', id);
    return null;
  }

  // Get the document's sub-category
  const docSubCategory = data.metadata?.metadata?.category;

  // If a specific subcategory was provided, validate it
  if (subCategorySlug && docSubCategory !== subCategorySlug) {
    return null;
  }

  // Determine technical vs non-technical for tabs
  const classif = data.metadata?.metadata?.classification?.toLowerCase();
  const type = classif === 'technical' ? 'technical' : 'non-technical';

  // Get the subcategory from the document metadata
  const subCategory = data.metadata?.metadata?.category || 'ai-machine-learning';

  // Get the main category from the subcategory using the mapping
  const documentMainCategorySlug = getMainCategoryFromSubCategory(subCategory) || 'technology';

  // Get the main category name (capitalize first letter)
  const mainCategory = documentMainCategorySlug.charAt(0).toUpperCase() + documentMainCategorySlug.slice(1);

  // Generate title and slug
  const title = data.metadata?.metadata?.title || 'Untitled Document';
  const slug = createSEOSlug(title);

  return {
    ...data,
    title,
    slug,
    type,
    subCategory,
    mainCategory,
    mainCategorySlug: documentMainCategorySlug
  } as SopDocument;
}

// Get all documents for the main page
export async function getAllDocuments() {
  const { data, error } = await supabase
    .from('sop_documents')
    .select(`
      id,
      metadata,
      created_at,
      image_url
    `)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching all documents:', error);
    return [];
  }

  if (!data || data.length === 0) {
    console.log('No documents found in database');
    return [];
  }

  const processedDocs = data.map((doc: any) => {
    // Get the subcategory from the document metadata
    const subCategory = doc.metadata?.metadata?.category || 'ai-machine-learning';

    // Get the main category from the subcategory using the mapping
    const mainCategorySlug = getMainCategoryFromSubCategory(subCategory) || 'technology';

    // Get the main category name (capitalize first letter)
    const mainCategory = mainCategorySlug.charAt(0).toUpperCase() + mainCategorySlug.slice(1);

    // Generate title and slug
    const title = doc.metadata?.metadata?.title || 'Untitled';
    const slug = createSEOSlug(title);

    return {
      id: doc.id,
      title,
      slug,
      subCategory: subCategory,
      mainCategory: mainCategory,
      mainCategorySlug: mainCategorySlug,
      classification: doc.metadata?.metadata?.classification || 'NON-TECHNICAL',
      metadata: doc.metadata,
      createdAt: doc.created_at,
      image_url: doc.image_url
    };
  });

  return processedDocs;
}


