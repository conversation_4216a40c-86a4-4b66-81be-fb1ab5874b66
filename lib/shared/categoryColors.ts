import { getMainCategoryFromSubCategory } from '@/lib/categoryMappings';

// Define the category colors based on the screenshot
export const categoryColors = {
  technology: {
    text: 'text-blue-600 dark:text-blue-400',
    bg: 'bg-blue-100 dark:bg-blue-900/20',
    border: 'border-blue-200 dark:border-blue-800',
    hover: 'hover:bg-blue-200 dark:hover:bg-blue-900/30',
    sparkle: 'text-blue-500',
  },
  business: {
    text: 'text-purple-600 dark:text-purple-400',
    bg: 'bg-purple-100 dark:bg-purple-900/20',
    border: 'border-purple-200 dark:border-purple-800',
    hover: 'hover:bg-purple-200 dark:hover:bg-purple-900/30',
    sparkle: 'text-purple-500',
  },
  health: {
    text: 'text-red-600 dark:text-red-400',
    bg: 'bg-red-100 dark:bg-red-900/20',
    border: 'border-red-200 dark:border-red-800',
    hover: 'hover:bg-red-200 dark:hover:bg-red-900/30',
    sparkle: 'text-red-500',
  },
  culture: {
    text: 'text-green-600 dark:text-green-400',
    bg: 'bg-green-100 dark:bg-green-900/20',
    border: 'border-green-200 dark:border-green-800',
    hover: 'hover:bg-green-200 dark:hover:bg-green-900/30',
    sparkle: 'text-green-500',
  },
  finance: {
    text: 'text-yellow-600 dark:text-yellow-400',
    bg: 'bg-yellow-100 dark:bg-yellow-900/20',
    border: 'border-yellow-200 dark:border-yellow-800',
    hover: 'hover:bg-yellow-200 dark:hover:bg-yellow-900/30',
    sparkle: 'text-yellow-500',
  },
  creative: {
    text: 'text-pink-600 dark:text-pink-400',
    bg: 'bg-pink-100 dark:bg-pink-900/20',
    border: 'border-pink-200 dark:border-pink-800',
    hover: 'hover:bg-pink-200 dark:hover:bg-pink-900/30',
    sparkle: 'text-pink-500',
  },
};

export type CategoryType = keyof typeof categoryColors;

/**
 * Get the main category slug for a given category or subcategory
 * This ensures subcategories inherit colors from their main category
 * @param categoryOrSubcategory The category or subcategory slug
 * @returns The main category slug
 */
export function getMainCategorySlug(categoryOrSubcategory: string): CategoryType {
  // If it's already a main category, return it
  if (categoryOrSubcategory.toLowerCase() in categoryColors) {
    return categoryOrSubcategory.toLowerCase() as CategoryType;
  }

  // If it's a subcategory, get its main category
  const mainCategorySlug = getMainCategoryFromSubCategory(categoryOrSubcategory);
  if (mainCategorySlug) {
    return mainCategorySlug as CategoryType;
  }

  // Default to technology if no match is found
  return 'technology';
}

/**
 * Get the sparkle color class for a specific category or subcategory
 * @param categoryOrSubcategory The category or subcategory slug
 * @returns The Tailwind CSS class for the sparkle color
 */
export function getCategorySparkleColor(categoryOrSubcategory: string): string {
  const mainCategorySlug = getMainCategorySlug(categoryOrSubcategory);
  return categoryColors[mainCategorySlug]?.sparkle || 'text-blue-500'; // Default to blue
}

/**
 * Get all color classes for a specific category or subcategory
 * @param categoryOrSubcategory The category or subcategory slug
 * @returns An object with all the color classes for the category
 */
export function getCategoryColorClasses(categoryOrSubcategory: string) {
  const mainCategorySlug = getMainCategorySlug(categoryOrSubcategory);
  return categoryColors[mainCategorySlug] || categoryColors.technology; // Default to technology
}
