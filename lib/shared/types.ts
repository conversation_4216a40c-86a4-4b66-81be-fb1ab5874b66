// API Response Types

// User and Auth
export interface UserProfile {
  id: string;
  email: string;
  username?: string;
  role: 'user' | 'admin' | 'superAdmin';
  active: boolean;
  created_at: string;
  updated_at: string;
  last_login_at?: string;
  available_credits: number;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in?: number;
  user?: UserProfile;
}

// Job Types
export type JobStatus = 'created' | 'pending' | 'running' | 'completed' | 'failed';
export type JobSourceType = 'youtube' | 'upload' | 'url';
export type JobPriority = 'low' | 'normal' | 'high';

export interface Job {
  id: string;
  user_id: string;
  status: JobStatus;
  source_url?: string;
  source_type?: JobSourceType;
  progress: number;
  format?: string;
  quality?: string;
  result?: Record<string, unknown>;
  error?: string;
  created_at: string;
  updated_at: string;
  started_at?: string;
  completed_at?: string;
  priority: JobPriority;
  content_hash?: string;
  transcription_path?: string;
  generate_sop: boolean;
}

export interface JobProgress {
  id: string;
  progress: number;
  status: JobStatus;
  message?: string;
}

// Transcription
export interface TranscriptionSegment {
  start: number;
  end: number;
  text: string;
  speaker?: string;
}

export interface Transcription {
  id: string;
  job_id: string;
  user_id: string;
  transcript: string;
  language?: string;
  segments?: TranscriptionSegment[];
  metadata?: any;
  created_at: string;
  updated_at: string;
  format?: string;
  storage_path?: string;
  storage_key?: string;
}

// SOP Document
export type SOPDocumentType = 'action_plan' | 'sop' | 'transcript_analysis';

// --- SOP Document Types ---

// Common interfaces
export interface ExecutiveSummaryData {
  core_insights?: string[];
  expected_outcomes?: string[];
  critical_considerations?: string[];
  strategic_recommendations?: string[];
}

export interface RiskItem {
  id?: string;         // non-technical
  risk_id?: string;    // technical
  description: string;
  mitigation: string;
  contingency: string;
}

export interface RiskAssessmentData {
    risks: RiskItem[];
}

export interface ConclusionData {
  key_findings?: string[];
  success_factors?: string[];
  next_steps?: string[];
}

export interface Phase {
  phase: string;
  description: string;
  steps: string[];
}

export interface TransformationRoadmapData {
    phases: Phase[];
}

export interface ActionItem {
  id: string;
  description: string;
  dependencies: string[];
  requirements: string[];
  acceptance_criteria?: string[]; // non-technical only
}

// Non-Technical Specific Interfaces
export interface BizIntroSummary {
    introduction?: string;
    executive_summary?: ExecutiveSummaryData;
}

export interface BizKeyTakeaways {
    key_takeaways?: string[];
}

export interface BizActionItems {
    action_items: ActionItem[]; // Note the nesting
}

export interface BizPracticalApplications {
    practical_applications?: string[];
}

export interface BizEmotionalSocialImpact {
    emotional_effects?: string[];
    social_consequences?: string[];
}

export interface BizBestPractice {
  practice: string;
  explanation?: string;
  expected_outcomes?: string;
  implementation_guide?: string;
  resource_requirements?: string;
  variations?: string[];
  expertise_level?: string;
}

export interface BizBestPracticesFramework {
    practices?: BizBestPractice[];
}

export interface BizResource {
  name: string;
  description: string;
  type: string;
  link?: string;
  estimated_cost?: string;
  resource_id?: string;
}

export interface BizResourcesData {
    resources?: BizResource[];
}

export interface BizContextSummary {
    context_summary?: string;
}

// Non-Technical Pipeline Output Structure
export interface NonTechnicalPipelineOutputsData {
  biz_intro_summary?: { biz_intro_summary: BizIntroSummary };
  biz_key_takeaways?: BizKeyTakeaways;
  biz_context_summary?: BizContextSummary;
  biz_risk_assessment?: { risk_assessment: RiskAssessmentData };
  biz_action_items?: { action_items: BizActionItems }; // Double nesting
  biz_best_practices?: { best_practices_framework: BizBestPracticesFramework };
  biz_practical_applications?: BizPracticalApplications;
  biz_transformation_roadmap?: { transformation_roadmap: TransformationRoadmapData };
  biz_emotional_or_social_impact?: { emotional_or_social_impact: BizEmotionalSocialImpact };
  biz_resources?: BizResourcesData;
  biz_conclusions?: { conclusions: ConclusionData };
}

// Technical Specific Interfaces
export interface TechIntroSummary {
    introduction?: string;
    executive_summary?: ExecutiveSummaryData;
}

export interface TechActionItems {
    action_items: ActionItem[]; // Direct array
}

export interface TechBestPractice {
  practice: string;
  variations?: string[];
  explanation?: string;
  expertise_level?: string; // Appears in tech JSON example too
  expected_outcomes?: string;
  implementation_guide?: string;
  resource_requirements?: string;
}

export interface TechBestPractices {
    practices?: TechBestPractice[]; // Direct array
}

export interface TechResource {
  name: string;
  url: string;
  usage: string;
}

export interface TechResourcesData {
  resources?: TechResource[]
};

export interface TechPracticalApplications {
  technical_applications?: string[];
  practical_applications?: string[]; // Added to support both naming conventions
}

// Technical Pipeline Output Structure
export interface TechPipelineOutputsData {
  tech_intro_summary?: { tech_intro_summary: TechIntroSummary };
  tech_action_items?: TechActionItems; // Direct array
  tech_risk_assessment?: { risk_assessment: RiskAssessmentData };
  tech_best_practices?: TechBestPractices; // Direct array
  tech_practical_applications?: TechPracticalApplications;
  tech_transformation_roadmap?: { transformation_roadmap: TransformationRoadmapData };
  tech_conclusions?: { conclusions: ConclusionData };

  // Support both nested and direct structures for tech_resources
  tech_resources?: { tech_resources: TechResourcesData } | TechResourcesData;

  // Add other tech-specific fields if they exist (e.g., tech_key_takeaways)
}

// Main SOP Document Type
export interface SopDocumentMetadata {
    title: string;
    domain?: string;
    classification?: 'TECHNICAL' | 'NON-TECHNICAL';
    primary_objective?: string;
    category?: string;        // This is actually the sub-category slug
    author?: string;          // Document author
}

export interface SopDocument {
    id: string;
    created_at: string; // ISO date string
    updated_at: string; // ISO date string
    user_id: string;
    type: 'technical' | 'non-technical';
    metadata: {
      metadata: SopDocumentMetadata; // Nested structure
    } | null; // Assuming the outer metadata object could be null
    pipeline_outputs: NonTechnicalPipelineOutputsData | TechPipelineOutputsData | null | Record<string, any>;
    // Additional properties for category mapping
    subCategory?: string;
    mainCategory?: string;
    mainCategorySlug?: string;
}

// Request Payloads
export interface DownloadRequest {
  url: string;
  format?: string;
  options?: string[];
  transcribe: boolean;
  priority?: JobPriority;
  webhook_url?: string;
}

export interface GenerateSOPRequest {
  job_id: string;
}

// Response Payloads
export interface JobCreatedResponse {
  message: string;
  job_id: string;
}

export interface SOPGeneratedResponse {
  message: string;
  sop_document_id: string;
}

// Subscription and Credits

export interface UserSubscription {
  price_id: string;
  active: boolean;
  expires_at?: string | null;
  credits_remaining: number;
}

export interface UserCredits {
  available: number;
  updated_at: string;
}

