// Main categories with their slugs
export const mainCategories = {
  Technology: 'technology',
  Business: 'business',
  Health: 'health',
  Culture: 'culture',
  Finance: 'finance',
  Creative: 'creative',
};

// Sub-categories mapped to main categories
export const subCategoryToMainCategoryMap: Record<string, string> = {
  // Technology
  'web-mobile-development': 'Technology',
  'ai-machine-learning': 'Technology',
  'cybersecurity-privacy': 'Technology',
  'devops-cloud': 'Technology',
  'hardware-iot': 'Technology',

  // Business
  'strategy-planning': 'Business',
  'leadership-management': 'Business',
  'marketing-sales': 'Business',
  'entrepreneurship': 'Business',
  'operations': 'Business',

  // Health
  'medical-research': 'Health',
  'wellness-fitness': 'Health',
  'mental-health': 'Health',
  'healthcare-systems': 'Health',
  'nutrition': 'Health',

  // Culture
  'arts-entertainment': 'Culture',
  'social-trends': 'Culture',
  'education': 'Culture',
  'media': 'Culture',
  'lifestyle': 'Culture',

  // Finance
  'investing': 'Finance',
  'cryptocurrency': 'Finance',
  'personal-finance': 'Finance',
  'banking': 'Finance',
  'economics': 'Finance',

  // Creative
  'design': 'Creative',
  'writing': 'Creative',
  'visual-arts': 'Creative',
  'music-audio': 'Creative',
  'film-video': 'Creative',
};

// Get main category slug from sub-category slug
export function getMainCategoryFromSubCategory(subCategorySlug: string): string | null {
  const mainCategory = subCategoryToMainCategoryMap[subCategorySlug];
  return mainCategory ? mainCategories[mainCategory as keyof typeof mainCategories] : null;
}

// Validate if a sub-category belongs to a main category
export function validateCategoryHierarchy(mainCategorySlug: string, subCategorySlug: string): boolean {
  const expectedMainCategory = subCategoryToMainCategoryMap[subCategorySlug];
  if (!expectedMainCategory) return false;

  return mainCategories[expectedMainCategory as keyof typeof mainCategories] === mainCategorySlug;
}

// Get all subcategories for a main category
export function getSubcategoriesForMainCategory(mainCategoryName: string): string[] {
  return Object.entries(subCategoryToMainCategoryMap)
    .filter(([_, category]) => category === mainCategoryName)
    .map(([subcategory]) => subcategory);
}
