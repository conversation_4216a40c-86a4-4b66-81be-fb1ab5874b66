{"name": "duvains<PERSON>", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "type-check": "tsc --noEmit"}, "dependencies": {"@mdx-js/loader": "^3.0.0", "@mdx-js/react": "^3.0.0", "@next/mdx": "^15.0.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/supabase-js": "^2.49.8", "@types/mdx": "^2.0.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.0.0", "framer-motion": "^11.0.0", "fuse.js": "^7.0.0", "gray-matter": "^4.0.3", "lucide-react": "^0.511.0", "next": "^15.0.3", "next-mdx-remote": "^5.0.0", "next-themes": "^0.2.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^9.0.0", "reading-time": "^1.5.0", "remark": "^15.0.1", "remark-html": "^16.0.1", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.10", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "autoprefixer": "^10.4.16", "eslint": "^8.0.0", "eslint-config-next": "^15.0.0", "postcss": "^8.4.32", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0", "tailwindcss": "^3.4.0", "typescript": "^5.0.0"}}